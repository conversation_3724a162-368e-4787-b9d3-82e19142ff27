import React, { createContext, useContext, useState, useEffect } from 'react';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

import { authAPI } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from stored tokens
  useEffect(() => {
    const initializeAuth = async () => {
      const accessToken = Cookies.get('access_token');
      const refreshToken = Cookies.get('refresh_token');

      if (accessToken) {
        try {
          // Set the token in API headers
          authAPI.setAuthToken(accessToken);
          
          // Get user profile
          const response = await authAPI.getProfile();
          setUser(response.data.user);
        } catch (error) {
          console.error('Failed to initialize auth:', error);
          
          // Try to refresh token
          if (refreshToken) {
            try {
              const refreshResponse = await authAPI.refreshToken(refreshToken);
              const newAccessToken = refreshResponse.data.access_token;
              
              Cookies.set('access_token', newAccessToken, { expires: 1 }); // 1 day
              authAPI.setAuthToken(newAccessToken);
              
              setUser(refreshResponse.data.user);
            } catch (refreshError) {
              console.error('Failed to refresh token:', refreshError);
              logout();
            }
          } else {
            logout();
          }
        }
      }
      
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email, password) => {
    try {
      const response = await authAPI.login(email, password);
      const { user, access_token, refresh_token } = response.data;

      // Store tokens
      Cookies.set('access_token', access_token, { expires: 1 }); // 1 day
      Cookies.set('refresh_token', refresh_token, { expires: 30 }); // 30 days

      // Set auth token in API headers
      authAPI.setAuthToken(access_token);

      setUser(user);
      toast.success('Login successful!');
      
      return { success: true, user };
    } catch (error) {
      const message = error.response?.data?.error || 'Login failed';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);
      const { user, access_token, refresh_token } = response.data;

      // Store tokens
      Cookies.set('access_token', access_token, { expires: 1 }); // 1 day
      Cookies.set('refresh_token', refresh_token, { expires: 30 }); // 30 days

      // Set auth token in API headers
      authAPI.setAuthToken(access_token);

      setUser(user);
      toast.success('Registration successful!');
      
      return { success: true, user };
    } catch (error) {
      const message = error.response?.data?.error || 'Registration failed';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const logout = () => {
    // Remove tokens
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');

    // Clear auth token from API headers
    authAPI.setAuthToken(null);

    setUser(null);
    toast.success('Logged out successfully');
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData);
      setUser(response.data.user);
      toast.success('Profile updated successfully!');
      return { success: true, user: response.data.user };
    } catch (error) {
      const message = error.response?.data?.error || 'Failed to update profile';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const changePassword = async (currentPassword, newPassword) => {
    try {
      await authAPI.changePassword(currentPassword, newPassword);
      toast.success('Password changed successfully!');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.error || 'Failed to change password';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const refreshAuthToken = async () => {
    try {
      const refreshToken = Cookies.get('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await authAPI.refreshToken(refreshToken);
      const newAccessToken = response.data.access_token;

      Cookies.set('access_token', newAccessToken, { expires: 1 });
      authAPI.setAuthToken(newAccessToken);

      return newAccessToken;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      logout();
      throw error;
    }
  };

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    refreshAuthToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
