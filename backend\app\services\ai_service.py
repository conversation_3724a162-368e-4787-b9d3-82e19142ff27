import requests
import json
from flask import current_app
from typing import List, Dict, Any


class AIService:
    """Service for AI operations using Ollama and other AI models."""
    
    def __init__(self):
        self.ollama_base_url = current_app.config.get('OLLAMA_BASE_URL', 'http://localhost:11434')
        self.ollama_model = current_app.config.get('OLLAMA_MODEL', 'llama2')
    
    def _call_ollama(self, prompt: str, model: str = None, system_prompt: str = None) -> str:
        """Make a request to Ollama API."""
        try:
            model = model or self.ollama_model
            url = f"{self.ollama_base_url}/api/generate"
            
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = requests.post(url, json=payload, timeout=300)  # 5 minute timeout
            response.raise_for_status()
            
            result = response.json()
            return result.get('response', '')
            
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Ollama API error: {e}")
            raise Exception(f"AI service unavailable: {e}")
        except Exception as e:
            current_app.logger.error(f"Error calling Ollama: {e}")
            raise
    
    def analyze_transcript_for_segments(self, transcript_text: str, video_duration: float) -> List[Dict[str, Any]]:
        """Analyze transcript to suggest key segments."""
        try:
            system_prompt = """You are an expert video content analyzer. Your task is to identify the most important and engaging segments from a video transcript that would make good clips for a summary video.

Focus on:
- Key insights, main points, or important information
- Engaging stories or examples
- Clear explanations of concepts
- Actionable advice or tips
- Interesting facts or statistics

Avoid:
- Repetitive content
- Filler words or long pauses
- Off-topic tangents
- Low-value small talk

Return your response as a JSON array of segments, each with:
- start_time: estimated start time in seconds
- end_time: estimated end time in seconds  
- title: brief descriptive title
- description: why this segment is important
- topics: array of relevant keywords/topics
- importance_score: score from 0.0 to 1.0"""

            prompt = f"""Analyze this video transcript and suggest 5-10 key segments for a summary video.

Video Duration: {video_duration} seconds

Transcript:
{transcript_text}

Return only valid JSON with the segment suggestions."""

            response = self._call_ollama(prompt, system_prompt=system_prompt)
            
            # Try to parse JSON response
            try:
                segments = json.loads(response)
                if not isinstance(segments, list):
                    raise ValueError("Response is not a list")
                
                # Validate and clean segments
                validated_segments = []
                for segment in segments:
                    if self._validate_segment(segment, video_duration):
                        validated_segments.append(segment)
                
                return validated_segments
                
            except (json.JSONDecodeError, ValueError) as e:
                current_app.logger.warning(f"Failed to parse AI response as JSON: {e}")
                # Fallback: create segments based on transcript length
                return self._create_fallback_segments(transcript_text, video_duration)
                
        except Exception as e:
            current_app.logger.error(f"Error analyzing transcript: {e}")
            # Return fallback segments
            return self._create_fallback_segments(transcript_text, video_duration)
    
    def _validate_segment(self, segment: Dict[str, Any], video_duration: float) -> bool:
        """Validate a segment suggestion."""
        required_fields = ['start_time', 'end_time', 'title', 'description']
        
        # Check required fields
        for field in required_fields:
            if field not in segment:
                return False
        
        # Validate timing
        start_time = segment.get('start_time', 0)
        end_time = segment.get('end_time', 0)
        
        if not isinstance(start_time, (int, float)) or not isinstance(end_time, (int, float)):
            return False
        
        if start_time < 0 or end_time <= start_time or end_time > video_duration:
            return False
        
        # Minimum segment length (5 seconds)
        if end_time - start_time < 5:
            return False
        
        return True
    
    def _create_fallback_segments(self, transcript_text: str, video_duration: float) -> List[Dict[str, Any]]:
        """Create fallback segments when AI analysis fails."""
        segments = []
        
        # Simple approach: divide video into equal segments
        num_segments = min(5, max(1, int(video_duration / 120)))  # 2-minute segments, max 5
        segment_duration = video_duration / num_segments
        
        for i in range(num_segments):
            start_time = i * segment_duration
            end_time = min((i + 1) * segment_duration, video_duration)
            
            segments.append({
                'start_time': start_time,
                'end_time': end_time,
                'title': f'Segment {i + 1}',
                'description': 'Auto-generated segment',
                'topics': [],
                'importance_score': 0.5
            })
        
        return segments
    
    def generate_video_title(self, transcript_text: str, max_length: int = 100) -> str:
        """Generate a title for the video based on transcript."""
        try:
            system_prompt = """You are an expert content creator. Generate engaging, clickable titles for video content based on transcripts. The title should be:
- Concise and clear
- Engaging and attention-grabbing
- Accurately represent the content
- Optimized for search and discovery"""

            prompt = f"""Based on this video transcript, generate a compelling title (max {max_length} characters):

{transcript_text[:2000]}  # Limit transcript length for prompt

Return only the title, nothing else."""

            response = self._call_ollama(prompt, system_prompt=system_prompt)
            
            # Clean and truncate response
            title = response.strip().strip('"').strip("'")
            if len(title) > max_length:
                title = title[:max_length].rsplit(' ', 1)[0] + '...'
            
            return title or "AI-Generated Video Summary"
            
        except Exception as e:
            current_app.logger.error(f"Error generating title: {e}")
            return "AI-Generated Video Summary"
    
    def generate_video_description(self, transcript_text: str, source_videos: List[str]) -> str:
        """Generate a description for the video."""
        try:
            system_prompt = """You are an expert content creator. Generate informative video descriptions that:
- Summarize the key points covered
- Are engaging and well-structured
- Include relevant keywords
- Provide value to viewers"""

            sources_text = "\n".join([f"- {video}" for video in source_videos])
            
            prompt = f"""Based on this video transcript, generate a compelling description:

{transcript_text[:2000]}

Source videos:
{sources_text}

Include a brief summary of key points and mention that this is a curated summary from the source videos."""

            response = self._call_ollama(prompt, system_prompt=system_prompt)
            
            return response.strip() or "AI-generated video summary from curated content."
            
        except Exception as e:
            current_app.logger.error(f"Error generating description: {e}")
            return "AI-generated video summary from curated content."
    
    def generate_video_tags(self, transcript_text: str, max_tags: int = 10) -> List[str]:
        """Generate tags for the video."""
        try:
            system_prompt = """You are an expert in video SEO and content tagging. Generate relevant tags that:
- Are specific and descriptive
- Help with discoverability
- Are commonly searched terms
- Relate directly to the content"""

            prompt = f"""Based on this video transcript, generate {max_tags} relevant tags:

{transcript_text[:1500]}

Return only a comma-separated list of tags, nothing else."""

            response = self._call_ollama(prompt, system_prompt=system_prompt)
            
            # Parse tags
            tags = [tag.strip() for tag in response.split(',')]
            tags = [tag for tag in tags if tag and len(tag) > 2]  # Filter out empty/short tags
            
            return tags[:max_tags]
            
        except Exception as e:
            current_app.logger.error(f"Error generating tags: {e}")
            return ["video", "summary", "ai-generated"]
    
    def check_ollama_health(self) -> bool:
        """Check if Ollama service is available."""
        try:
            url = f"{self.ollama_base_url}/api/tags"
            response = requests.get(url, timeout=10)
            return response.status_code == 200
        except:
            return False
