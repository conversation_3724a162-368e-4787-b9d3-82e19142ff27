# UniDynamics Setup Script for Windows
# Run this script in PowerShell as Administrator

Write-Host "🚀 UniDynamics Setup Script" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

$prerequisites = @{
    "python" = "Python 3.9+"
    "node" = "Node.js 18+"
    "psql" = "PostgreSQL 13+"
    "redis-cli" = "Redis 6+"
    "ffmpeg" = "FFmpeg 4.4+"
    "ollama" = "Ollama"
}

$missing = @()

foreach ($cmd in $prerequisites.Keys) {
    if (Test-Command $cmd) {
        Write-Host "✅ $($prerequisites[$cmd]) found" -ForegroundColor Green
    } else {
        Write-Host "❌ $($prerequisites[$cmd]) not found" -ForegroundColor Red
        $missing += $cmd
    }
}

if ($missing.Count -gt 0) {
    Write-Host "`n⚠️  Missing prerequisites:" -ForegroundColor Yellow
    foreach ($cmd in $missing) {
        Write-Host "   - $($prerequisites[$cmd])" -ForegroundColor Red
    }
    Write-Host "`nPlease install missing prerequisites and run this script again." -ForegroundColor Yellow
    Write-Host "See SETUP_AND_TEST_GUIDE.md for installation instructions." -ForegroundColor Cyan
    exit 1
}

# Create project directories
Write-Host "`n📁 Creating project structure..." -ForegroundColor Yellow
$projectRoot = Get-Location

# Backend setup
Write-Host "`n🔧 Setting up backend..." -ForegroundColor Yellow
Set-Location "$projectRoot\backend"

# Create virtual environment
if (!(Test-Path "venv")) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Cyan
    python -m venv venv
}

# Activate virtual environment and install dependencies
Write-Host "Installing Python dependencies..." -ForegroundColor Cyan
& "venv\Scripts\activate.ps1"
pip install -r requirements.txt

# Create .env file if it doesn't exist
if (!(Test-Path ".env")) {
    Write-Host "Creating backend .env file..." -ForegroundColor Cyan
    @"
# Database
DATABASE_URL=postgresql://unidynamics:dev_password_123@localhost:5432/unidynamics_dev

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=your-super-secret-jwt-key-for-development-$(Get-Random)

# Flask
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=True

# AI Services
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# File Storage
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=2147483648

# YouTube API (optional)
YOUTUBE_CLIENT_SECRETS_FILE=config/youtube_client_secrets.json
YOUTUBE_REDIRECT_URI=http://localhost:3000/auth/youtube/callback
"@ | Out-File -FilePath ".env" -Encoding UTF8
}

# Create uploads directory
if (!(Test-Path "uploads")) {
    New-Item -ItemType Directory -Path "uploads"
}

# Create config directory
if (!(Test-Path "config")) {
    New-Item -ItemType Directory -Path "config"
}

# Frontend setup
Write-Host "`n🎨 Setting up frontend..." -ForegroundColor Yellow
Set-Location "$projectRoot\frontend"

# Install npm dependencies
Write-Host "Installing Node.js dependencies..." -ForegroundColor Cyan
npm install

# Create .env file if it doesn't exist
if (!(Test-Path ".env")) {
    Write-Host "Creating frontend .env file..." -ForegroundColor Cyan
    @"
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_ENVIRONMENT=development
REACT_APP_DEBUG=true
"@ | Out-File -FilePath ".env" -Encoding UTF8
}

# Database setup
Write-Host "`n🗄️  Setting up database..." -ForegroundColor Yellow
Set-Location "$projectRoot"

# Check if PostgreSQL is running
$pgRunning = Get-Process postgres -ErrorAction SilentlyContinue
if (!$pgRunning) {
    Write-Host "Starting PostgreSQL..." -ForegroundColor Cyan
    Start-Service postgresql*
}

# Create database and user
Write-Host "Creating database and user..." -ForegroundColor Cyan
$createDbScript = @"
DO `$`$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'unidynamics_dev') THEN
        CREATE DATABASE unidynamics_dev;
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'unidynamics') THEN
        CREATE USER unidynamics WITH PASSWORD 'dev_password_123';
    END IF;
    
    GRANT ALL PRIVILEGES ON DATABASE unidynamics_dev TO unidynamics;
END
`$`$;
"@

$createDbScript | psql -U postgres -d postgres

# Initialize database
Write-Host "Initializing database schema..." -ForegroundColor Cyan
Set-Location "$projectRoot\backend"
& "venv\Scripts\activate.ps1"

# Check if migrations exist
if (!(Test-Path "migrations")) {
    flask db init
}

flask db migrate -m "Initial migration"
flask db upgrade

# Start Redis if not running
Write-Host "`n🔴 Checking Redis..." -ForegroundColor Yellow
$redisRunning = Get-Process redis-server -ErrorAction SilentlyContinue
if (!$redisRunning) {
    Write-Host "Starting Redis..." -ForegroundColor Cyan
    Start-Process redis-server -WindowStyle Hidden
    Start-Sleep 3
}

# Check Ollama and pull model
Write-Host "`n🤖 Setting up Ollama..." -ForegroundColor Yellow
try {
    $ollamaList = ollama list 2>$null
    if ($ollamaList -notmatch "llama2") {
        Write-Host "Pulling Llama2 model (this may take a while)..." -ForegroundColor Cyan
        ollama pull llama2
    } else {
        Write-Host "✅ Llama2 model already available" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Could not connect to Ollama. Make sure it's installed and running." -ForegroundColor Yellow
}

# Create startup scripts
Write-Host "`n📝 Creating startup scripts..." -ForegroundColor Yellow
Set-Location $projectRoot

# Backend startup script
@"
@echo off
echo Starting UniDynamics Backend Services...
cd /d "$projectRoot\backend"
call venv\Scripts\activate.bat

echo Starting Flask API...
start "Flask API" cmd /k "python app.py"

echo Starting Celery Worker...
start "Celery Worker" cmd /k "celery -A app.celery worker --loglevel=info --pool=solo"

echo Backend services started!
echo Flask API: http://localhost:5000
echo Check the opened windows for logs.
pause
"@ | Out-File -FilePath "start-backend.bat" -Encoding ASCII

# Frontend startup script
@"
@echo off
echo Starting UniDynamics Frontend...
cd /d "$projectRoot\frontend"
echo Starting React development server...
npm start
"@ | Out-File -FilePath "start-frontend.bat" -Encoding ASCII

# Complete startup script
@"
@echo off
echo 🚀 Starting Complete UniDynamics Platform...
echo.

echo Starting backend services...
start "Backend" cmd /c "$projectRoot\start-backend.bat"

echo Waiting for backend to initialize...
timeout /t 10 /nobreak > nul

echo Starting frontend...
start "Frontend" cmd /c "$projectRoot\start-frontend.bat"

echo.
echo ✅ UniDynamics is starting up!
echo.
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:5000
echo.
echo Check the opened windows for logs and status.
echo.
pause
"@ | Out-File -FilePath "start-unidynamics.bat" -Encoding ASCII

# Create test script
@"
@echo off
echo 🧪 UniDynamics Quick Test Script
echo.

echo Testing backend API...
curl -s http://localhost:5000/health
if %errorlevel% equ 0 (
    echo ✅ Backend API is responding
) else (
    echo ❌ Backend API is not responding
)

echo.
echo Testing frontend...
curl -s http://localhost:3000 > nul
if %errorlevel% equ 0 (
    echo ✅ Frontend is responding
) else (
    echo ❌ Frontend is not responding
)

echo.
echo Testing database connection...
psql -U unidynamics -d unidynamics_dev -c "SELECT 1;" > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Database connection successful
) else (
    echo ❌ Database connection failed
)

echo.
echo Testing Redis...
redis-cli ping > nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis is responding
) else (
    echo ❌ Redis is not responding
)

echo.
echo 🎯 Test complete! Check results above.
pause
"@ | Out-File -FilePath "test-services.bat" -Encoding ASCII

Write-Host "`n🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Backend environment configured" -ForegroundColor Green
Write-Host "✅ Frontend environment configured" -ForegroundColor Green
Write-Host "✅ Database created and initialized" -ForegroundColor Green
Write-Host "✅ Startup scripts created" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 To start UniDynamics:" -ForegroundColor Cyan
Write-Host "   Double-click: start-unidynamics.bat" -ForegroundColor White
Write-Host ""
Write-Host "🧪 To test services:" -ForegroundColor Cyan
Write-Host "   Double-click: test-services.bat" -ForegroundColor White
Write-Host ""
Write-Host "📖 For detailed testing instructions:" -ForegroundColor Cyan
Write-Host "   See: SETUP_AND_TEST_GUIDE.md" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Access URLs:" -ForegroundColor Yellow
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend:  http://localhost:5000" -ForegroundColor White

Set-Location $projectRoot
