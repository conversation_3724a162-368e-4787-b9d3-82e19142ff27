import os
import time
import whisper
import openai
from flask import current_app


class TranscriptionService:
    """Service for video transcription using Whisper."""
    
    def __init__(self):
        self.openai_api_key = current_app.config.get('OPENAI_API_KEY')
        self.whisper_model = None
        
        # Initialize OpenAI client if API key is available
        if self.openai_api_key:
            openai.api_key = self.openai_api_key
    
    def load_whisper_model(self, model_name='base'):
        """Load Whisper model for local transcription."""
        try:
            if self.whisper_model is None:
                current_app.logger.info(f"Loading Whisper model: {model_name}")
                self.whisper_model = whisper.load_model(model_name)
            return self.whisper_model
        except Exception as e:
            current_app.logger.error(f"Error loading Whisper model: {e}")
            raise
    
    def transcribe_with_local_whisper(self, audio_file_path, model_name='base'):
        """Transcribe audio using local Whisper model."""
        try:
            start_time = time.time()
            
            # Load model
            model = self.load_whisper_model(model_name)
            
            # Transcribe
            current_app.logger.info(f"Starting transcription of {audio_file_path}")
            result = model.transcribe(audio_file_path)
            
            processing_time = time.time() - start_time
            
            # Format segments
            segments = []
            for segment in result['segments']:
                segments.append({
                    'start': segment['start'],
                    'end': segment['end'],
                    'text': segment['text'].strip(),
                    'confidence': segment.get('avg_logprob', 0.0)
                })
            
            return {
                'full_text': result['text'],
                'segments': segments,
                'language': result['language'],
                'processing_time': processing_time,
                'model': model_name
            }
            
        except Exception as e:
            current_app.logger.error(f"Error with local Whisper transcription: {e}")
            raise
    
    def transcribe_with_openai_api(self, audio_file_path):
        """Transcribe audio using OpenAI Whisper API."""
        try:
            if not self.openai_api_key:
                raise Exception("OpenAI API key not configured")
            
            start_time = time.time()
            
            current_app.logger.info(f"Starting OpenAI API transcription of {audio_file_path}")
            
            with open(audio_file_path, 'rb') as audio_file:
                # Use the new OpenAI client
                client = openai.OpenAI(api_key=self.openai_api_key)
                
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="verbose_json",
                    timestamp_granularities=["segment"]
                )
            
            processing_time = time.time() - start_time
            
            # Format segments
            segments = []
            if hasattr(transcript, 'segments') and transcript.segments:
                for segment in transcript.segments:
                    segments.append({
                        'start': segment['start'],
                        'end': segment['end'],
                        'text': segment['text'].strip(),
                        'confidence': 1.0  # OpenAI API doesn't provide confidence scores
                    })
            
            return {
                'full_text': transcript.text,
                'segments': segments,
                'language': transcript.language if hasattr(transcript, 'language') else 'unknown',
                'processing_time': processing_time,
                'model': 'whisper-1'
            }
            
        except Exception as e:
            current_app.logger.error(f"Error with OpenAI API transcription: {e}")
            raise
    
    def extract_audio_from_video(self, video_file_path, audio_file_path):
        """Extract audio from video file using FFmpeg."""
        try:
            import subprocess
            
            # Use FFmpeg to extract audio
            cmd = [
                'ffmpeg',
                '-i', video_file_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM 16-bit
                '-ar', '16000',  # 16kHz sample rate (Whisper's preferred)
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                audio_file_path
            ]
            
            current_app.logger.info(f"Extracting audio: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            if not os.path.exists(audio_file_path):
                raise Exception("Audio extraction failed - output file not created")
            
            return audio_file_path
            
        except subprocess.CalledProcessError as e:
            current_app.logger.error(f"FFmpeg error: {e.stderr}")
            raise Exception(f"Audio extraction failed: {e.stderr}")
        except Exception as e:
            current_app.logger.error(f"Error extracting audio: {e}")
            raise
    
    def transcribe_video(self, video_file_path, use_api=False, model_name='base'):
        """Transcribe video file (extracts audio first)."""
        try:
            # Create temporary audio file
            audio_file_path = video_file_path.rsplit('.', 1)[0] + '_audio.wav'
            
            try:
                # Extract audio
                self.extract_audio_from_video(video_file_path, audio_file_path)
                
                # Transcribe audio
                if use_api and self.openai_api_key:
                    result = self.transcribe_with_openai_api(audio_file_path)
                else:
                    result = self.transcribe_with_local_whisper(audio_file_path, model_name)
                
                return result
                
            finally:
                # Clean up temporary audio file
                if os.path.exists(audio_file_path):
                    os.remove(audio_file_path)
                    
        except Exception as e:
            current_app.logger.error(f"Error transcribing video: {e}")
            raise
    
    def get_transcript_summary(self, transcript_text, max_length=500):
        """Get a summary of the transcript."""
        if len(transcript_text) <= max_length:
            return transcript_text
        
        # Simple truncation with ellipsis
        return transcript_text[:max_length].rsplit(' ', 1)[0] + '...'
    
    def search_transcript(self, segments, query, case_sensitive=False):
        """Search for text in transcript segments."""
        if not case_sensitive:
            query = query.lower()
        
        matches = []
        for segment in segments:
            text = segment['text']
            if not case_sensitive:
                text = text.lower()
            
            if query in text:
                matches.append(segment)
        
        return matches
