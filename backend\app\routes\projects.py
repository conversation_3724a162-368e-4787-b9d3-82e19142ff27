from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import desc

from app import db
from app.models.user import User
from app.models.project import Project, ProjectStatus
from app.models.video import Video
from app.models.segment import Segment

projects_bp = Blueprint('projects', __name__)


@projects_bp.route('', methods=['GET'])
@jwt_required()
def get_projects():
    """Get all projects for the current user."""
    try:
        current_user_id = get_jwt_identity()
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 50)
        
        # Filter parameters
        status = request.args.get('status')
        search = request.args.get('search')
        
        # Build query
        query = Project.query.filter_by(user_id=current_user_id)
        
        if status:
            try:
                status_enum = ProjectStatus(status)
                query = query.filter_by(status=status_enum)
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
        
        if search:
            query = query.filter(
                Project.title.ilike(f'%{search}%') |
                Project.description.ilike(f'%{search}%')
            )
        
        # Order by creation date (newest first)
        query = query.order_by(desc(Project.created_at))
        
        # Paginate
        projects = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'projects': [project.to_dict() for project in projects.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': projects.total,
                'pages': projects.pages,
                'has_next': projects.has_next,
                'has_prev': projects.has_prev
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get projects error: {str(e)}")
        return jsonify({'error': 'Failed to get projects'}), 500


@projects_bp.route('', methods=['POST'])
@jwt_required()
def create_project():
    """Create a new project."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        if not data.get('title'):
            return jsonify({'error': 'Title is required'}), 400
        
        # Create project
        project = Project(
            user_id=current_user_id,
            title=data['title'],
            description=data.get('description'),
            max_duration=data.get('max_duration', 600),
            include_intro=data.get('include_intro', False),
            include_outro=data.get('include_outro', False)
        )
        
        db.session.add(project)
        db.session.commit()
        
        return jsonify({
            'message': 'Project created successfully',
            'project': project.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create project error: {str(e)}")
        return jsonify({'error': 'Failed to create project'}), 500


@projects_bp.route('/<int:project_id>', methods=['GET'])
@jwt_required()
def get_project(project_id):
    """Get a specific project with full details."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Include videos and segments in response
        return jsonify({
            'project': project.to_dict(include_videos=True, include_segments=True)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get project error: {str(e)}")
        return jsonify({'error': 'Failed to get project'}), 500


@projects_bp.route('/<int:project_id>', methods=['PUT'])
@jwt_required()
def update_project(project_id):
    """Update a project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        data = request.get_json()
        
        # Update allowed fields
        allowed_fields = [
            'title', 'description', 'max_duration', 
            'include_intro', 'include_outro'
        ]
        
        for field in allowed_fields:
            if field in data:
                setattr(project, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'message': 'Project updated successfully',
            'project': project.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Update project error: {str(e)}")
        return jsonify({'error': 'Failed to update project'}), 500


@projects_bp.route('/<int:project_id>', methods=['DELETE'])
@jwt_required()
def delete_project(project_id):
    """Delete a project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Delete project (cascade will handle related records)
        db.session.delete(project)
        db.session.commit()
        
        return jsonify({'message': 'Project deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete project error: {str(e)}")
        return jsonify({'error': 'Failed to delete project'}), 500


@projects_bp.route('/<int:project_id>/videos', methods=['GET'])
@jwt_required()
def get_project_videos(project_id):
    """Get all videos for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        videos = Video.query.filter_by(project_id=project_id).all()
        
        return jsonify({
            'videos': [video.to_dict(include_transcript=True, include_segments=True) 
                      for video in videos]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get project videos error: {str(e)}")
        return jsonify({'error': 'Failed to get project videos'}), 500


@projects_bp.route('/<int:project_id>/segments', methods=['GET'])
@jwt_required()
def get_project_segments(project_id):
    """Get all segments for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Filter parameters
        selected_only = request.args.get('selected_only', 'false').lower() == 'true'
        ai_suggested_only = request.args.get('ai_suggested_only', 'false').lower() == 'true'
        
        query = Segment.query.filter_by(project_id=project_id)
        
        if selected_only:
            query = query.filter_by(is_selected=True)
        
        if ai_suggested_only:
            query = query.filter_by(is_ai_suggested=True)
        
        # Order by timeline position for selected segments, otherwise by start time
        if selected_only:
            query = query.order_by(Segment.timeline_position.asc())
        else:
            query = query.order_by(Segment.start_time.asc())
        
        segments = query.all()
        
        return jsonify({
            'segments': [segment.to_dict() for segment in segments]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get project segments error: {str(e)}")
        return jsonify({'error': 'Failed to get project segments'}), 500


@projects_bp.route('/<int:project_id>/segments/select', methods=['POST'])
@jwt_required()
def select_segments(project_id):
    """Select segments for inclusion in final video."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        data = request.get_json()
        segment_selections = data.get('segments', [])
        
        if not segment_selections:
            return jsonify({'error': 'No segments provided'}), 400
        
        # Update segment selections
        for selection in segment_selections:
            segment_id = selection.get('id')
            is_selected = selection.get('selected', False)
            timeline_position = selection.get('timeline_position')
            
            segment = Segment.query.filter_by(
                id=segment_id, project_id=project_id
            ).first()
            
            if segment:
                segment.select(is_selected, timeline_position)
        
        # Update project status if segments are selected
        selected_count = Segment.query.filter_by(
            project_id=project_id, is_selected=True
        ).count()
        
        if selected_count > 0 and project.status == ProjectStatus.DRAFT:
            project.update_status(ProjectStatus.DRAFT, 0.3)  # 30% progress
        
        return jsonify({
            'message': 'Segments updated successfully',
            'selected_count': selected_count
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Select segments error: {str(e)}")
        return jsonify({'error': 'Failed to update segments'}), 500


@projects_bp.route('/<int:project_id>/status', methods=['GET'])
@jwt_required()
def get_project_status(project_id):
    """Get project processing status."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Get video processing status
        videos = Video.query.filter_by(project_id=project_id).all()
        video_statuses = [video.status.value for video in videos]
        
        return jsonify({
            'project_status': project.status.value,
            'progress': project.progress,
            'video_statuses': video_statuses,
            'total_videos': len(videos),
            'selected_segments': Segment.query.filter_by(
                project_id=project_id, is_selected=True
            ).count()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get project status error: {str(e)}")
        return jsonify({'error': 'Failed to get project status'}), 500
