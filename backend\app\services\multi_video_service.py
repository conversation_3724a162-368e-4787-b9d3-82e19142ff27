import os
import subprocess
import tempfile
from typing import List, Dict, Any, Optional
from flask import current_app

from app.models.project import Project
from app.models.video import Video
from app.models.segment import Segment
from app.services.storage_service import StorageService


class MultiVideoService:
    """Service for handling multi-video compilation and advanced video processing."""
    
    def __init__(self):
        self.storage_service = StorageService()
    
    def create_multi_video_timeline(self, project_id: int, segments_data: List[Dict]) -> Dict[str, Any]:
        """Create a timeline for multi-video compilation."""
        try:
            project = Project.query.get(project_id)
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Update project compilation mode
            project.compilation_mode = 'multi'
            
            # Process segments and assign timeline positions
            timeline = []
            current_position = 0.0
            
            for i, segment_data in enumerate(segments_data):
                segment = Segment.query.get(segment_data['segment_id'])
                if not segment:
                    continue
                
                # Update segment timeline position
                segment.timeline_position = i
                segment.is_selected = True
                
                # Add transition duration to position calculation
                if i > 0:
                    transition_duration = segment_data.get('transition_duration', project.transition_duration)
                    current_position += transition_duration
                
                timeline_entry = {
                    'segment_id': segment.id,
                    'video_id': segment.video_id,
                    'start_time': segment.start_time,
                    'end_time': segment.end_time,
                    'duration': segment.duration,
                    'timeline_start': current_position,
                    'timeline_end': current_position + segment.duration,
                    'transition_in': segment_data.get('transition_in', 'cut'),
                    'transition_out': segment_data.get('transition_out', 'cut'),
                    'source_video': segment.video.to_dict() if segment.video else None
                }
                
                timeline.append(timeline_entry)
                current_position += segment.duration
            
            # Save changes
            from app import db
            db.session.commit()
            
            return {
                'timeline': timeline,
                'total_duration': current_position,
                'segment_count': len(timeline),
                'video_count': len(set(entry['video_id'] for entry in timeline))
            }
            
        except Exception as e:
            current_app.logger.error(f"Error creating multi-video timeline: {str(e)}")
            raise
    
    def render_multi_video(self, project_id: int) -> str:
        """Render final video from multiple source videos with transitions."""
        try:
            project = Project.query.get(project_id)
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Get selected segments in timeline order
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).order_by(Segment.timeline_position).all()
            
            if not segments:
                raise ValueError("No segments selected for rendering")
            
            # Group segments by source video
            video_segments = {}
            for segment in segments:
                if segment.video_id not in video_segments:
                    video_segments[segment.video_id] = []
                video_segments[segment.video_id].append(segment)
            
            # Create temporary directory for processing
            with tempfile.TemporaryDirectory() as temp_dir:
                # Step 1: Extract segments from each source video
                segment_files = []
                for video_id, video_segments_list in video_segments.items():
                    video = Video.query.get(video_id)
                    if not video or not video.file_path:
                        continue
                    
                    for segment in video_segments_list:
                        segment_file = self._extract_segment(
                            video.file_path, 
                            segment, 
                            temp_dir
                        )
                        if segment_file:
                            segment_files.append({
                                'file': segment_file,
                                'segment': segment,
                                'timeline_position': segment.timeline_position
                            })
                
                # Step 2: Sort by timeline position
                segment_files.sort(key=lambda x: x['timeline_position'])
                
                # Step 3: Create filter complex for transitions
                filter_complex = self._create_transition_filter(segment_files, project)
                
                # Step 4: Render final video
                output_file = self._render_final_video(
                    segment_files, 
                    filter_complex, 
                    project, 
                    temp_dir
                )
                
                # Step 5: Upload to storage
                final_url = self.storage_service.upload_file(
                    output_file, 
                    f"projects/{project_id}/output.mp4"
                )
                
                # Update project
                project.output_url = final_url
                project.output_filename = f"{project.title}_summary.mp4"
                project.output_duration = sum(s.duration for s in segments)
                
                from app import db
                db.session.commit()
                
                return final_url
                
        except Exception as e:
            current_app.logger.error(f"Error rendering multi-video: {str(e)}")
            raise
    
    def _extract_segment(self, video_path: str, segment: Segment, temp_dir: str) -> Optional[str]:
        """Extract a single segment from a video file."""
        try:
            output_file = os.path.join(temp_dir, f"segment_{segment.id}.mp4")
            
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(segment.start_time),
                '-t', str(segment.duration),
                '-c', 'copy',
                output_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                current_app.logger.error(f"FFmpeg error extracting segment: {result.stderr}")
                return None
            
            return output_file
            
        except Exception as e:
            current_app.logger.error(f"Error extracting segment {segment.id}: {str(e)}")
            return None
    
    def _create_transition_filter(self, segment_files: List[Dict], project: Project) -> str:
        """Create FFmpeg filter complex for transitions between segments."""
        if len(segment_files) <= 1:
            return "[0:v][0:a]"
        
        filter_parts = []
        video_inputs = []
        audio_inputs = []
        
        for i, segment_file in enumerate(segment_files):
            segment = segment_file['segment']
            
            # Video processing
            if segment.transition_in == 'fade' and i > 0:
                fade_duration = segment.transition_in_duration or project.transition_duration
                filter_parts.append(f"[{i}:v]fade=t=in:st=0:d={fade_duration}[v{i}]")
                video_inputs.append(f"[v{i}]")
            else:
                video_inputs.append(f"[{i}:v]")
            
            # Audio processing
            if segment.transition_in == 'fade' and i > 0:
                fade_duration = segment.transition_in_duration or project.transition_duration
                filter_parts.append(f"[{i}:a]afade=t=in:st=0:d={fade_duration}[a{i}]")
                audio_inputs.append(f"[a{i}]")
            else:
                audio_inputs.append(f"[{i}:a]")
        
        # Concatenate all segments
        video_concat = "".join(video_inputs) + f"concat=n={len(segment_files)}:v=1:a=0[outv]"
        audio_concat = "".join(audio_inputs) + f"concat=n={len(segment_files)}:v=0:a=1[outa]"
        
        filter_parts.extend([video_concat, audio_concat])
        
        return ";".join(filter_parts)
    
    def _render_final_video(self, segment_files: List[Dict], filter_complex: str, 
                          project: Project, temp_dir: str) -> str:
        """Render the final video with all segments and transitions."""
        output_file = os.path.join(temp_dir, "final_output.mp4")
        
        # Build FFmpeg command
        cmd = ['ffmpeg', '-y']
        
        # Add input files
        for segment_file in segment_files:
            cmd.extend(['-i', segment_file['file']])
        
        # Add intro if specified
        if project.intro_file_url:
            intro_path = self.storage_service.download_file(project.intro_file_url, temp_dir)
            if intro_path:
                cmd.extend(['-i', intro_path])
        
        # Add outro if specified
        if project.outro_file_url:
            outro_path = self.storage_service.download_file(project.outro_file_url, temp_dir)
            if outro_path:
                cmd.extend(['-i', outro_path])
        
        # Add background music if specified
        if project.background_music_url:
            music_path = self.storage_service.download_file(project.background_music_url, temp_dir)
            if music_path:
                cmd.extend(['-i', music_path])
        
        # Add filter complex
        if len(segment_files) > 1:
            cmd.extend(['-filter_complex', filter_complex])
            cmd.extend(['-map', '[outv]', '-map', '[outa]'])
        else:
            cmd.extend(['-c', 'copy'])
        
        # Output settings
        cmd.extend([
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-preset', 'medium',
            '-crf', '23',
            output_file
        ])
        
        # Execute FFmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"FFmpeg rendering failed: {result.stderr}")
        
        return output_file
    
    def add_intro_outro(self, project_id: int, intro_file: Optional[str] = None, 
                       outro_file: Optional[str] = None) -> bool:
        """Add intro and/or outro files to a project."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return False
            
            if intro_file:
                intro_url = self.storage_service.upload_file(
                    intro_file, 
                    f"projects/{project_id}/intro.mp4"
                )
                project.intro_file_url = intro_url
                project.include_intro = True
            
            if outro_file:
                outro_url = self.storage_service.upload_file(
                    outro_file, 
                    f"projects/{project_id}/outro.mp4"
                )
                project.outro_file_url = outro_url
                project.include_outro = True
            
            from app import db
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error adding intro/outro: {str(e)}")
            return False
    
    def add_background_music(self, project_id: int, music_file: str, volume: float = 0.2) -> bool:
        """Add background music to a project."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return False
            
            music_url = self.storage_service.upload_file(
                music_file, 
                f"projects/{project_id}/background_music.mp3"
            )
            
            project.background_music_url = music_url
            project.background_music_volume = max(0.0, min(1.0, volume))
            
            from app import db
            db.session.commit()
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error adding background music: {str(e)}")
            return False
