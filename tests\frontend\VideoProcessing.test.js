import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

// Mock the API
jest.mock('../../frontend/src/services/api', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

// Mock Video.js
jest.mock('video.js', () => {
  const mockPlayer = {
    on: jest.fn(),
    src: jest.fn(),
    currentTime: jest.fn(() => 0),
    duration: jest.fn(() => 100),
    videoWidth: jest.fn(() => 1920),
    videoHeight: jest.fn(() => 1080),
    dispose: jest.fn(),
  };
  
  return jest.fn(() => mockPlayer);
});

import VideoEditorPage from '../../frontend/src/pages/Editor/VideoEditorPage';
import VideoPlayer from '../../frontend/src/components/VideoPlayer/VideoPlayer';
import TranscriptViewer from '../../frontend/src/components/Transcript/TranscriptViewer';
import SegmentTimeline from '../../frontend/src/components/Timeline/SegmentTimeline';
import SegmentSelector from '../../frontend/src/components/Segments/SegmentSelector';
import ProcessingStatus from '../../frontend/src/components/Processing/ProcessingStatus';
import DownloadInterface from '../../frontend/src/components/Output/DownloadInterface';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockProject = {
  id: 1,
  title: 'Test Project',
  description: 'Test project description',
  status: 'draft'
};

const mockVideo = {
  id: 1,
  title: 'Test Video',
  youtube_url: 'https://www.youtube.com/watch?v=test',
  file_url: '/test-video.mp4',
  status: 'completed'
};

const mockTranscript = {
  segments: [
    {
      id: 1,
      start_time: 0,
      end_time: 10,
      text: 'This is the first segment of the video transcript.'
    },
    {
      id: 2,
      start_time: 10,
      end_time: 20,
      text: 'This is the second segment with more content.'
    }
  ]
};

const mockSegments = [
  {
    id: 1,
    start_time: 0,
    end_time: 10,
    title: 'Introduction',
    summary: 'Video introduction segment',
    importance_score: 0.8,
    is_ai_suggested: true,
    is_selected: false,
    topics: ['introduction', 'overview']
  },
  {
    id: 2,
    start_time: 10,
    end_time: 20,
    title: 'Main Content',
    summary: 'Main content of the video',
    importance_score: 0.9,
    is_ai_suggested: true,
    is_selected: true,
    topics: ['main', 'content']
  }
];

describe('Video Processing Components', () => {
  describe('VideoPlayer', () => {
    test('renders video player with controls', () => {
      render(
        <TestWrapper>
          <VideoPlayer
            src="/test-video.mp4"
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
          />
        </TestWrapper>
      );

      expect(screen.getByRole('application')).toBeInTheDocument();
    });

    test('displays segment overlays', () => {
      render(
        <TestWrapper>
          <VideoPlayer
            src="/test-video.mp4"
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
          />
        </TestWrapper>
      );

      // Check if segment overlays are rendered
      const overlays = document.querySelectorAll('[title*="Segment"]');
      expect(overlays.length).toBeGreaterThan(0);
    });
  });

  describe('TranscriptViewer', () => {
    test('renders transcript with search functionality', () => {
      render(
        <TestWrapper>
          <TranscriptViewer
            transcript={mockTranscript}
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
          />
        </TestWrapper>
      );

      expect(screen.getByPlaceholderText('Search transcript...')).toBeInTheDocument();
      expect(screen.getByText(/This is the first segment/)).toBeInTheDocument();
    });

    test('filters transcript based on search term', async () => {
      render(
        <TestWrapper>
          <TranscriptViewer
            transcript={mockTranscript}
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
          />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText('Search transcript...');
      fireEvent.change(searchInput, { target: { value: 'second' } });

      await waitFor(() => {
        expect(screen.getByText(/This is the second segment/)).toBeInTheDocument();
        expect(screen.queryByText(/This is the first segment/)).not.toBeInTheDocument();
      });
    });
  });

  describe('SegmentTimeline', () => {
    test('renders timeline with segments', () => {
      render(
        <TestWrapper>
          <SegmentTimeline
            duration={100}
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
            currentTime={15}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Video Timeline')).toBeInTheDocument();
      expect(screen.getByText('Duration: 1:40')).toBeInTheDocument();
    });

    test('displays current time indicator', () => {
      render(
        <TestWrapper>
          <SegmentTimeline
            duration={100}
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
            currentTime={15}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Current: 0:15')).toBeInTheDocument();
    });
  });

  describe('SegmentSelector', () => {
    const mockOnToggle = jest.fn();
    const mockOnSelectAll = jest.fn();
    const mockOnSelectNone = jest.fn();

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('renders segment list with selection controls', () => {
      render(
        <TestWrapper>
          <SegmentSelector
            segments={mockSegments}
            selectedSegments={[mockSegments[1]]}
            onSegmentToggle={mockOnToggle}
            onSelectAll={mockOnSelectAll}
            onSelectNone={mockOnSelectNone}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Video Segments')).toBeInTheDocument();
      expect(screen.getByText('Select All')).toBeInTheDocument();
      expect(screen.getByText('Select None')).toBeInTheDocument();
      expect(screen.getByText('Introduction')).toBeInTheDocument();
      expect(screen.getByText('Main Content')).toBeInTheDocument();
    });

    test('calls onSelectAll when Select All button is clicked', () => {
      render(
        <TestWrapper>
          <SegmentSelector
            segments={mockSegments}
            selectedSegments={[]}
            onSegmentToggle={mockOnToggle}
            onSelectAll={mockOnSelectAll}
            onSelectNone={mockOnSelectNone}
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByText('Select All'));
      expect(mockOnSelectAll).toHaveBeenCalledTimes(1);
    });

    test('filters segments by search term', async () => {
      render(
        <TestWrapper>
          <SegmentSelector
            segments={mockSegments}
            selectedSegments={[]}
            onSegmentToggle={mockOnToggle}
            onSelectAll={mockOnSelectAll}
            onSelectNone={mockOnSelectNone}
          />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText('Search segments...');
      fireEvent.change(searchInput, { target: { value: 'introduction' } });

      await waitFor(() => {
        expect(screen.getByText('Introduction')).toBeInTheDocument();
        expect(screen.queryByText('Main Content')).not.toBeInTheDocument();
      });
    });
  });

  describe('ProcessingStatus', () => {
    test('renders processing status for active video', () => {
      const mockStatus = {
        status: 'transcribing',
        has_transcript: false,
        segments_count: 0,
        ai_suggested_segments: 0
      };

      render(
        <TestWrapper>
          <ProcessingStatus
            videoId={1}
            status={mockStatus}
            showDetails={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Processing Status')).toBeInTheDocument();
      expect(screen.getByText('Transcribing Audio')).toBeInTheDocument();
    });

    test('shows completed status with segment information', () => {
      const mockStatus = {
        status: 'completed',
        has_transcript: true,
        segments_count: 5,
        ai_suggested_segments: 3
      };

      render(
        <TestWrapper>
          <ProcessingStatus
            videoId={1}
            status={mockStatus}
            showDetails={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Completed')).toBeInTheDocument();
      expect(screen.getByText(/Found 5 segments, 3 AI-suggested/)).toBeInTheDocument();
    });
  });

  describe('DownloadInterface', () => {
    const mockDownloadData = {
      download_url: 'https://example.com/video.mp4',
      filename: 'summary_video.mp4',
      duration: 120,
      file_size: 1024000,
      attribution: 'Video created with UniDynamics\nSource: Test Video'
    };

    test('renders download interface with video information', () => {
      render(
        <TestWrapper>
          <DownloadInterface
            downloadData={mockDownloadData}
            onDownload={jest.fn()}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Download Your Video')).toBeInTheDocument();
      expect(screen.getByText('summary_video.mp4')).toBeInTheDocument();
      expect(screen.getByText('2:00')).toBeInTheDocument();
      expect(screen.getByText('Download Video')).toBeInTheDocument();
    });

    test('shows attribution information', () => {
      render(
        <TestWrapper>
          <DownloadInterface
            downloadData={mockDownloadData}
            onDownload={jest.fn()}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Attribution Information')).toBeInTheDocument();
      expect(screen.getByText(/Video created with UniDynamics/)).toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  test('video processing workflow integration', async () => {
    // This would test the complete workflow from adding a video to downloading the result
    // For now, we'll just verify the main components can be rendered together
    
    const mockApi = require('../../frontend/src/services/api');
    mockApi.get.mockResolvedValue({
      data: {
        project: mockProject,
        videos: [mockVideo]
      }
    });

    render(
      <TestWrapper>
        <VideoEditorPage />
      </TestWrapper>
    );

    // The component should render without crashing
    // In a real test, we would mock the useParams hook to provide a projectId
    expect(document.body).toBeInTheDocument();
  });
});
