# UniDynamics Development Guide

This guide covers the development setup, architecture, and workflows for UniDynamics.

## Architecture Overview

UniDynamics follows a modern microservices architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Flask Backend  │    │  Celery Workers │
│                 │    │                 │    │                 │
│  - User Interface│◄──►│  - REST API     │◄──►│  - Video Proc.  │
│  - Video Editor │    │  - Auth         │    │  - AI Analysis  │
│  - Timeline     │    │  - Business Logic│   │  - Transcription│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   PostgreSQL    │    │      Redis      │
         │              │                 │    │                 │
         │              │  - User Data    │    │  - Task Queue   │
         │              │  - Projects     │    │  - Cache        │
         │              │  - Videos       │    │  - Sessions     │
         │              │  - Segments     │    └─────────────────┘
         │              └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Cloud Storage  │    │     Ollama      │    │   YouTube API   │
│                 │    │                 │    │                 │
│  - Video Files  │    │  - Local LLMs   │    │  - Metadata     │
│  - Outputs      │    │  - AI Analysis  │    │  - License Info │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Technology Stack

### Backend
- **Framework**: Flask 2.3+ with SQLAlchemy
- **Database**: PostgreSQL 15+
- **Task Queue**: Celery with Redis
- **AI/ML**: Ollama (local LLMs), OpenAI Whisper
- **Video Processing**: FFmpeg, MoviePy
- **Authentication**: JWT with Flask-JWT-Extended

### Frontend
- **Framework**: React 18+ with Material-UI
- **State Management**: React Context + Hooks
- **Routing**: React Router v6
- **HTTP Client**: Axios
- **Video Player**: Video.js
- **Forms**: React Hook Form

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Cloud Storage**: AWS S3 / Google Cloud Storage
- **Monitoring**: Sentry (optional)
- **CI/CD**: GitHub Actions (planned)

## Development Setup

### Prerequisites
- Docker & Docker Compose
- Node.js 16+ (for local frontend development)
- Python 3.9+ (for local backend development)
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd unidynamics
   ```

2. **Run setup script**
   ```bash
   # Linux/macOS
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   
   # Windows PowerShell
   .\scripts\setup.ps1
   ```

3. **Choose development mode**
   - **Full Docker**: Everything in containers (easiest)
   - **Hybrid**: Services in Docker, code local (recommended)
   - **Local**: Everything local (advanced)

### Manual Setup

#### Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Initialize database
python run.py init-db

# Run development server
python run.py

# Run Celery worker (separate terminal)
celery -A app.celery worker --loglevel=info
```

#### Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Start development server
npm start
```

## Project Structure

```
unidynamics/
├── backend/                 # Flask backend
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API endpoints
│   │   ├── services/       # Business logic
│   │   ├── tasks/          # Celery tasks
│   │   └── utils/          # Utilities
│   ├── migrations/         # Database migrations
│   ├── config.py          # Configuration
│   └── run.py             # Application entry point
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API clients
│   │   ├── contexts/      # React contexts
│   │   └── utils/         # Utilities
│   └── public/            # Static assets
├── docker/                # Docker configuration
├── scripts/               # Setup and deployment scripts
├── docs/                  # Documentation
└── tests/                 # Test files
```

## Development Workflow

### 1. Feature Development
1. Create feature branch from `develop`
2. Implement backend changes (models, routes, services)
3. Write tests for backend functionality
4. Implement frontend changes (components, pages)
5. Test integration between frontend and backend
6. Submit pull request

### 2. Database Changes
1. Modify models in `backend/app/models/`
2. Generate migration: `flask db migrate -m "Description"`
3. Review migration file
4. Apply migration: `flask db upgrade`

### 3. API Development
1. Define routes in `backend/app/routes/`
2. Implement business logic in `backend/app/services/`
3. Add Celery tasks in `backend/app/tasks/` if needed
4. Update API documentation
5. Test with frontend integration

### 4. Frontend Development
1. Create components in `frontend/src/components/`
2. Implement pages in `frontend/src/pages/`
3. Add API calls in `frontend/src/services/`
4. Update routing if needed
5. Test responsive design

## Testing

### Backend Testing
```bash
cd backend
python -m pytest tests/
```

### Frontend Testing
```bash
cd frontend
npm test
```

### Integration Testing
```bash
# Start all services
docker-compose up -d

# Run integration tests
npm run test:integration
```

## Debugging

### Backend Debugging
- Use Flask debug mode: `FLASK_ENV=development`
- Check logs: `docker-compose logs backend`
- Database queries: Enable SQLAlchemy logging
- Celery tasks: `docker-compose logs celery-worker`

### Frontend Debugging
- React DevTools browser extension
- Check console for errors
- Network tab for API calls
- Redux DevTools (if using Redux)

### Common Issues

1. **Database connection errors**
   - Check PostgreSQL is running
   - Verify DATABASE_URL in .env
   - Ensure database exists

2. **Celery tasks not running**
   - Check Redis connection
   - Verify Celery worker is running
   - Check task imports

3. **AI/Ollama issues**
   - Ensure Ollama is running
   - Check if models are downloaded
   - Verify OLLAMA_BASE_URL

4. **Video processing errors**
   - Check FFmpeg installation
   - Verify file permissions
   - Check available disk space

## Performance Optimization

### Backend
- Use database indexing for frequent queries
- Implement caching with Redis
- Optimize Celery task performance
- Use connection pooling

### Frontend
- Implement code splitting
- Optimize bundle size
- Use React.memo for expensive components
- Implement virtual scrolling for large lists

### Video Processing
- Use appropriate video quality settings
- Implement progress tracking
- Optimize FFmpeg parameters
- Consider parallel processing

## Security Considerations

- Always validate user inputs
- Use parameterized database queries
- Implement rate limiting
- Secure file uploads
- Use HTTPS in production
- Regularly update dependencies
- Implement proper error handling

## Deployment

See `docs/DEPLOYMENT.md` for production deployment instructions.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Getting Help

- Check existing documentation
- Search GitHub issues
- Create a new issue with detailed description
- Join our Discord community (if available)
