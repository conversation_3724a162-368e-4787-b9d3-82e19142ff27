# UniDynamics - Local Development Project Plan

**Version:** 2.0 (Local Development)
**Date:** December 2024

## 1. Project Overview

**Project Name:** UniDynamics

**Mission:** To empower users to transform lengthy, legally re-publishable YouTube videos into concise, engaging, and high-impact summaries, often by combining the best parts of multiple sources into a single, coherent piece.

**Vision:** To be the leading platform for intelligent video summarization and remixing, saving viewers' time, enhancing information retention, and maximizing the reach of valuable content.

**Target Users:**
*   Educators & Students
*   Researchers & Academics
*   Content Curators & Marketers
*   Lifelong Learners
*   Corporate Trainers
*   Anyone needing to efficiently consume or present video-based information.

**Value Proposition:**
*   **Time Saving:** Condenses hours of video into minutes.
*   **Increased Comprehension:** Focuses on key takeaways.
*   **Enhanced Reach:** Makes long-form content more accessible to wider audiences.
*   **Creative Remixing:** Allows for novel combinations of educational/informative content.
*   **Legal Compliance:** Built-in checks and guides for using re-publishable content.

## 2. Local Development Approach

**Philosophy:** Develop and test locally first, then deploy to production. This approach provides:
- **Faster Development:** No container overhead or network latency
- **Better Debugging:** Direct access to all services and logs
- **Cost Efficiency:** No cloud costs during development
- **Learning Opportunity:** Understanding of all system components
- **Flexibility:** Easy to modify and experiment with configurations

## 3. Technology Stack (Local Development)

### Backend
- **Framework:** Python Flask 2.3+ with SQLAlchemy ORM
- **Database:** PostgreSQL 15+ (local installation)
- **Task Queue:** Celery with Redis 7+ (local installation)
- **AI/ML:** Ollama (local installation) with Llama2/Mistral models
- **Transcription:** OpenAI Whisper (local processing)
- **Video Processing:** FFmpeg (local installation)
- **Storage:** Local file system for development
- **Authentication:** JWT tokens with Flask-JWT-Extended

### Frontend
- **Framework:** React 18+ with Material-UI 5+
- **State Management:** React Context + Hooks
- **Routing:** React Router v6
- **HTTP Client:** Axios
- **Video Player:** Video.js
- **Build Tool:** Create React App

### Local Infrastructure Requirements
- **Python:** 3.9+ (recommended: 3.11)
- **Node.js:** 16+ (recommended: 18 LTS)
- **PostgreSQL:** 13+ (recommended: 15)
- **Redis:** 6+ (recommended: 7)
- **FFmpeg:** 4.4+ (recommended: latest)
- **Ollama:** Latest version
- **Git:** For version control

## 4. Local Development Setup

### 4.1. Prerequisites Installation

#### Windows
1. **PostgreSQL:** Download from postgresql.org
2. **Redis:** Use WSL2 or Windows Redis port
3. **Ollama:** Download from ollama.ai
4. **FFmpeg:** Download and add to PATH
5. **Python:** Download from python.org
6. **Node.js:** Download from nodejs.org

#### macOS
```bash
brew install postgresql@15 redis ffmpeg python@3.11 node@18
# Install Ollama from ollama.ai
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib redis-server ffmpeg python3.11 nodejs npm
# Install Ollama from ollama.ai
```

### 4.2. Service Configuration

#### PostgreSQL Setup
```sql
-- Create database and user
CREATE DATABASE unidynamics;
CREATE USER unidynamics WITH PASSWORD 'unidynamics_dev';
GRANT ALL PRIVILEGES ON DATABASE unidynamics TO unidynamics;
```

#### Redis Setup
- Start Redis server: `redis-server`
- Verify: `redis-cli ping`

#### Ollama Setup
```bash
# Start Ollama service
ollama serve

# Pull required models
ollama pull llama2
ollama pull mistral
```

### 4.3. Application Setup

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.local .env
# Edit .env with your local configuration
python run.py init-db
```

#### Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env
# Edit .env with local API URL
```

## 5. Development Workflow

### 5.1. Starting Development Environment

1. **Start Local Services:**
   ```bash
   # Terminal 1: PostgreSQL (usually auto-starts)
   # Terminal 2: Redis
   redis-server
   
   # Terminal 3: Ollama
   ollama serve
   ```

2. **Start Application Services:**
   ```bash
   # Terminal 4: Backend API
   cd backend && source venv/bin/activate && python run.py
   
   # Terminal 5: Celery Worker
   cd backend && source venv/bin/activate && celery -A app.celery worker --loglevel=info
   
   # Terminal 6: Frontend
   cd frontend && npm start
   ```

### 5.2. Development URLs
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **PostgreSQL:** localhost:5432
- **Redis:** localhost:6379
- **Ollama:** http://localhost:11434

## 6. Current Implementation Status

### ✅ Completed Components (60% of MVP)
- **Backend Infrastructure:** Flask app, database models, API routes
- **User Authentication:** Registration, login, JWT tokens
- **Project Management:** CRUD operations, status tracking
- **YouTube Integration:** URL validation, metadata extraction
- **Video Processing:** Download, transcription, storage
- **AI Integration:** Ollama connection, segment analysis
- **Basic Frontend:** React app, authentication, dashboard

### ⚠️ Partially Implemented (30% of MVP)
- **Video Rendering:** Logic exists but needs testing
- **Frontend UI:** Basic components exist but incomplete
- **Error Handling:** Basic implementation needs improvement

### ❌ Missing Components (10% of MVP)
- **Complete Video Processing UI:** Timeline, segment selection
- **Download Interface:** Final video download and preview
- **End-to-End Testing:** Full workflow verification

## 7. Phase 1 MVP Completion Plan

### 7.1. Immediate Priorities (Next 4-6 weeks)

#### Week 1-2: Complete Video Processing UI
- [ ] Video URL input interface
- [ ] Video player integration (Video.js)
- [ ] Transcript display with highlighting
- [ ] Segment timeline visualization
- [ ] AI suggestion display
- [ ] Segment selection controls

#### Week 3: Processing Status and Error Handling
- [ ] Real-time processing status updates
- [ ] Progress indicators and feedback
- [ ] Error handling and user notifications
- [ ] Task cancellation functionality

#### Week 4: Download and Output System
- [ ] Download interface with attribution
- [ ] Video preview functionality
- [ ] File management and cleanup
- [ ] Share and export options

#### Week 5-6: Testing and Polish
- [ ] End-to-end workflow testing
- [ ] Bug fixes and performance optimization
- [ ] User experience improvements
- [ ] Documentation updates

### 7.2. Success Criteria for MVP
- [ ] User can register and login
- [ ] User can add YouTube videos to projects
- [ ] System downloads and transcribes videos locally
- [ ] AI suggests video segments using Ollama
- [ ] User can select and edit segments
- [ ] System renders final video using FFmpeg
- [ ] User can download completed video with attribution

## 8. Local Development Benefits

### 8.1. Advantages
- **Fast Iteration:** No container build/deploy cycles
- **Easy Debugging:** Direct access to all services
- **Cost Effective:** No cloud costs during development
- **Full Control:** Complete visibility into all components
- **Offline Capable:** Can develop without internet connection

### 8.2. Considerations
- **Setup Complexity:** Initial local service installation
- **Environment Consistency:** Different developer setups
- **Resource Usage:** All services running on local machine
- **Production Differences:** Local vs. production environment gaps

## 9. Transition to Production

### 9.1. Production Deployment Strategy
1. **Containerization:** Docker containers for consistent deployment
2. **Cloud Services:** Managed PostgreSQL, Redis, and storage
3. **Scaling:** Horizontal scaling for API and workers
4. **Monitoring:** Application performance monitoring
5. **Security:** Production security configurations

### 9.2. Infrastructure Requirements
- **Database:** Managed PostgreSQL (AWS RDS, Google Cloud SQL)
- **Cache/Queue:** Managed Redis (AWS ElastiCache, Google Memorystore)
- **Storage:** Cloud storage (AWS S3, Google Cloud Storage)
- **Compute:** Container orchestration (Kubernetes, Docker Swarm)
- **AI Processing:** GPU instances for Ollama (if needed)

## 10. Risk Mitigation

### 10.1. Development Risks
- **Service Dependencies:** Document all local service requirements
- **Data Loss:** Regular database backups during development
- **Performance Issues:** Monitor resource usage on development machines
- **Configuration Drift:** Maintain consistent environment configurations

### 10.2. Technical Risks
- **Local Service Failures:** Provide troubleshooting guides
- **Resource Constraints:** Optimize for local development hardware
- **Integration Issues:** Regular end-to-end testing
- **Scalability Concerns:** Design with production scaling in mind

## 11. Next Steps

### 11.1. Immediate Actions
1. Complete video processing UI implementation
2. Implement end-to-end testing
3. Fix remaining bugs and issues
4. Improve error handling and user feedback
5. Document local development setup

### 11.2. Medium-term Goals
1. Complete Phase 1 MVP
2. User testing and feedback collection
3. Performance optimization
4. Production deployment preparation
5. Phase 2 planning (multi-video features)

This local development approach provides a solid foundation for building UniDynamics while maintaining development velocity and cost efficiency.
