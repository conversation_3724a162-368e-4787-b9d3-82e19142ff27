# UniDynamics Local Development Setup Guide

This guide will help you set up UniDynamics for local development without Docker containers.

## Prerequisites

### Required Software Versions

- **Python**: 3.9+ (recommended: 3.11)
- **Node.js**: 16+ (recommended: 18 LTS)
- **PostgreSQL**: 13+ (recommended: 15)
- **Redis**: 6+ (recommended: 7)
- **FFmpeg**: 4.4+ (recommended: latest)
- **Ollama**: Latest version

## Installation Instructions

### 1. PostgreSQL Setup

#### Windows
1. Download PostgreSQL from: https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Add PostgreSQL bin directory to PATH (usually `C:\Program Files\PostgreSQL\15\bin`)

#### macOS
```bash
# Using Homebrew
brew install postgresql@15
brew services start postgresql@15

# Or using MacPorts
sudo port install postgresql15-server
sudo port load postgresql15-server
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Database Setup
```bash
# Create database and user
sudo -u postgres psql

# In PostgreSQL shell:
CREATE DATABASE unidynamics;
CREATE USER unidynamics WITH PASSWORD 'unidynamics_dev';
GRANT ALL PRIVILEGES ON DATABASE unidynamics TO unidynamics;
\q
```

### 2. Redis Setup

#### Windows
1. Download Redis from: https://github.com/microsoftarchive/redis/releases
2. Extract and run `redis-server.exe`
3. Or use WSL2:
   ```bash
   wsl --install
   # In WSL:
   sudo apt-get update
   sudo apt-get install redis-server
   redis-server
   ```

#### macOS
```bash
brew install redis
brew services start redis
```

#### Linux
```bash
sudo apt-get install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 3. Ollama Setup

#### All Platforms
1. Download from: https://ollama.ai/download
2. Install the application
3. Open terminal and run:
   ```bash
   ollama serve
   ```
4. In another terminal, pull required models:
   ```bash
   ollama pull llama2
   ollama pull mistral  # Optional
   ```

### 4. FFmpeg Setup

#### Windows
1. Download from: https://ffmpeg.org/download.html#build-windows
2. Extract to a folder (e.g., `C:\ffmpeg`)
3. Add `C:\ffmpeg\bin` to your PATH environment variable

#### macOS
```bash
brew install ffmpeg
```

#### Linux
```bash
sudo apt-get install ffmpeg
```

### 5. Python Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd unidynamics/backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 6. Node.js Environment Setup

```bash
cd ../frontend
npm install
```

## Configuration

### 1. Backend Configuration

Copy the local environment template:
```bash
cd backend
cp .env.local .env
```

Edit `.env` file with your local settings:
```bash
# Update database credentials if different
DATABASE_URL=postgresql://unidynamics:unidynamics_dev@localhost:5432/unidynamics

# Verify other settings match your local setup
REDIS_URL=redis://localhost:6379/0
OLLAMA_BASE_URL=http://localhost:11434
```

### 2. Frontend Configuration

```bash
cd frontend
cp .env.example .env
```

Edit `.env` file:
```bash
REACT_APP_API_BASE_URL=http://localhost:5000/api
```

## Starting the Application

### 1. Start Required Services

Make sure these services are running:
- PostgreSQL (usually starts automatically)
- Redis: `redis-server`
- Ollama: `ollama serve`

### 2. Initialize Database

```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
python run.py init-db
```

### 3. Start Backend Services

Terminal 1 - Flask API:
```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
python run.py
```

Terminal 2 - Celery Worker:
```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
celery -A app.celery worker --loglevel=info
```

### 4. Start Frontend

Terminal 3 - React App:
```bash
cd frontend
npm start
```

## Verification

1. **Backend API**: http://localhost:5000/health
2. **Frontend**: http://localhost:3000
3. **PostgreSQL**: `psql -h localhost -U unidynamics -d unidynamics`
4. **Redis**: `redis-cli ping`
5. **Ollama**: `curl http://localhost:11434/api/tags`

## Troubleshooting

### Common Issues

1. **PostgreSQL Connection Error**
   - Check if PostgreSQL is running: `pg_isready`
   - Verify credentials in `.env` file
   - Check if database exists: `psql -l`

2. **Redis Connection Error**
   - Check if Redis is running: `redis-cli ping`
   - Verify Redis URL in `.env` file

3. **Ollama Not Responding**
   - Check if Ollama is running: `ollama list`
   - Restart Ollama service: `ollama serve`

4. **FFmpeg Not Found**
   - Verify FFmpeg is in PATH: `ffmpeg -version`
   - Update FFMPEG_PATH in `.env` if needed

5. **Python Dependencies**
   - Update pip: `pip install --upgrade pip`
   - Install build tools if needed (Windows: Visual Studio Build Tools)

### Performance Tips

1. **PostgreSQL Optimization**
   ```sql
   -- Increase shared_buffers in postgresql.conf
   shared_buffers = 256MB
   ```

2. **Redis Optimization**
   ```bash
   # Increase memory limit in redis.conf
   maxmemory 512mb
   ```

3. **Ollama Performance**
   - Use GPU acceleration if available
   - Allocate sufficient RAM (8GB+ recommended)

## Development Workflow

1. **Making Changes**
   - Backend: Flask auto-reloads in debug mode
   - Frontend: React hot-reloads automatically
   - Celery: Restart worker after code changes

2. **Database Changes**
   ```bash
   # Create migration
   flask db migrate -m "Description"
   
   # Apply migration
   flask db upgrade
   ```

3. **Testing**
   ```bash
   # Backend tests
   cd backend
   python -m pytest
   
   # Frontend tests
   cd frontend
   npm test
   ```

## Next Steps

Once your local environment is running:
1. Create your first user account
2. Test video upload functionality
3. Verify AI analysis is working
4. Check video processing pipeline

For production deployment, see [DEPLOYMENT.md](DEPLOYMENT.md).
