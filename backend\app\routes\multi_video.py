from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import os

from app import db
from app.models.project import Project
from app.models.segment import Segment
from app.services.multi_video_service import MultiVideoService
from app.tasks.video_tasks import render_multi_video_task

multi_video_bp = Blueprint('multi_video', __name__)
multi_video_service = MultiVideoService()


@multi_video_bp.route('/timeline', methods=['POST'])
@jwt_required()
def create_timeline():
    """Create a multi-video timeline."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data.get('project_id'):
            return jsonify({'error': 'Project ID is required'}), 400
        
        project_id = data['project_id']
        segments_data = data.get('segments', [])
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Create timeline
        timeline_data = multi_video_service.create_multi_video_timeline(
            project_id, segments_data
        )
        
        return jsonify({
            'message': 'Timeline created successfully',
            'timeline': timeline_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Create timeline error: {str(e)}")
        return jsonify({'error': 'Failed to create timeline'}), 500


@multi_video_bp.route('/render', methods=['POST'])
@jwt_required()
def render_multi_video():
    """Start rendering a multi-video compilation."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data.get('project_id'):
            return jsonify({'error': 'Project ID is required'}), 400
        
        project_id = data['project_id']
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Check if project has selected segments
        selected_segments = Segment.query.filter_by(
            project_id=project_id, is_selected=True
        ).count()
        
        if selected_segments == 0:
            return jsonify({'error': 'No segments selected for rendering'}), 400
        
        # Update project status
        project.update_status('processing', 0.1)
        
        # Start rendering task
        task = render_multi_video_task.delay(project_id)
        
        return jsonify({
            'message': 'Multi-video rendering started',
            'task_id': task.id,
            'project_id': project_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Render multi-video error: {str(e)}")
        return jsonify({'error': 'Failed to start rendering'}), 500


@multi_video_bp.route('/segments/<int:segment_id>/transitions', methods=['PUT'])
@jwt_required()
def update_segment_transitions(segment_id):
    """Update transition settings for a segment."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Get segment and verify ownership
        segment = db.session.query(Segment).join(Project).filter(
            Segment.id == segment_id,
            Project.user_id == current_user_id
        ).first()
        
        if not segment:
            return jsonify({'error': 'Segment not found'}), 404
        
        # Update transition settings
        if 'transition_in' in data:
            segment.transition_in = data['transition_in']
        if 'transition_out' in data:
            segment.transition_out = data['transition_out']
        if 'transition_in_duration' in data:
            segment.transition_in_duration = data['transition_in_duration']
        if 'transition_out_duration' in data:
            segment.transition_out_duration = data['transition_out_duration']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Transition settings updated',
            'segment': segment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update transitions error: {str(e)}")
        return jsonify({'error': 'Failed to update transitions'}), 500


@multi_video_bp.route('/projects/<int:project_id>/intro', methods=['POST'])
@jwt_required()
def upload_intro(project_id):
    """Upload intro video for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Validate file type
        allowed_extensions = {'mp4', 'avi', 'mov', 'mkv'}
        if not ('.' in file.filename and 
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        file.save(temp_path)
        
        # Add intro to project
        success = multi_video_service.add_intro_outro(
            project_id, intro_file=temp_path
        )
        
        # Clean up temp file
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        if success:
            return jsonify({
                'message': 'Intro uploaded successfully',
                'project': project.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Failed to upload intro'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Upload intro error: {str(e)}")
        return jsonify({'error': 'Failed to upload intro'}), 500


@multi_video_bp.route('/projects/<int:project_id>/outro', methods=['POST'])
@jwt_required()
def upload_outro(project_id):
    """Upload outro video for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Validate file type
        allowed_extensions = {'mp4', 'avi', 'mov', 'mkv'}
        if not ('.' in file.filename and 
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        file.save(temp_path)
        
        # Add outro to project
        success = multi_video_service.add_intro_outro(
            project_id, outro_file=temp_path
        )
        
        # Clean up temp file
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        if success:
            return jsonify({
                'message': 'Outro uploaded successfully',
                'project': project.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Failed to upload outro'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Upload outro error: {str(e)}")
        return jsonify({'error': 'Failed to upload outro'}), 500


@multi_video_bp.route('/projects/<int:project_id>/background-music', methods=['POST'])
@jwt_required()
def upload_background_music(project_id):
    """Upload background music for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Validate file type
        allowed_extensions = {'mp3', 'wav', 'aac', 'm4a'}
        if not ('.' in file.filename and 
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Get volume setting
        volume = float(request.form.get('volume', 0.2))
        
        # Save file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        file.save(temp_path)
        
        # Add background music to project
        success = multi_video_service.add_background_music(
            project_id, temp_path, volume
        )
        
        # Clean up temp file
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        if success:
            return jsonify({
                'message': 'Background music uploaded successfully',
                'project': project.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Failed to upload background music'}), 500
            
    except Exception as e:
        current_app.logger.error(f"Upload background music error: {str(e)}")
        return jsonify({'error': 'Failed to upload background music'}), 500


@multi_video_bp.route('/projects/<int:project_id>/settings', methods=['PUT'])
@jwt_required()
def update_project_settings(project_id):
    """Update multi-video project settings."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Update settings
        allowed_fields = [
            'compilation_mode', 'transition_type', 'transition_duration',
            'background_music_volume', 'auto_chapters', 'youtube_upload_enabled'
        ]
        
        for field in allowed_fields:
            if field in data:
                setattr(project, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'message': 'Project settings updated',
            'project': project.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update project settings error: {str(e)}")
        return jsonify({'error': 'Failed to update settings'}), 500
