#!/usr/bin/env python3
"""
Basic functionality tests for UniDynamics
This file tests core functionality without requiring external services
"""

import pytest
import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app import create_app, db
from app.models.user import User
from app.models.project import Project, ProjectStatus
from app.models.video import Video, VideoStatus, LicenseType
from app.models.segment import Segment
from app.models.transcript import Transcript


@pytest.fixture
def app():
    """Create test app."""
    app = create_app('testing')
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()


@pytest.fixture
def client(app):
    """Create test client."""
    return app.test_client()


@pytest.fixture
def auth_headers(client):
    """Create authenticated user and return auth headers."""
    # Register user
    response = client.post('/api/auth/register', json={
        'email': '<EMAIL>',
        'username': 'testuser',
        'password': 'TestPassword123',
        'first_name': 'Test',
        'last_name': 'User'
    })
    
    assert response.status_code == 201
    data = response.get_json()
    token = data['access_token']
    
    return {'Authorization': f'Bearer {token}'}


class TestUserModel:
    """Test User model functionality."""
    
    def test_user_creation(self, app):
        """Test user creation."""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                username='testuser',
                password='password123'
            )
            
            assert user.email == '<EMAIL>'
            assert user.username == 'testuser'
            assert user.check_password('password123')
            assert not user.check_password('wrongpassword')
    
    def test_user_full_name(self, app):
        """Test user full name property."""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                username='testuser',
                first_name='John',
                last_name='Doe'
            )
            
            assert user.full_name == 'John Doe'
    
    def test_user_to_dict(self, app):
        """Test user serialization."""
        with app.app_context():
            user = User(
                email='<EMAIL>',
                username='testuser',
                first_name='John',
                last_name='Doe'
            )
            
            data = user.to_dict()
            assert data['email'] == '<EMAIL>'
            assert data['username'] == 'testuser'
            assert data['full_name'] == 'John Doe'
            assert 'password_hash' not in data


class TestProjectModel:
    """Test Project model functionality."""
    
    def test_project_creation(self, app):
        """Test project creation."""
        with app.app_context():
            user = User(email='<EMAIL>', username='testuser')
            db.session.add(user)
            db.session.commit()
            
            project = Project(
                user_id=user.id,
                title='Test Project',
                description='A test project'
            )
            
            assert project.title == 'Test Project'
            assert project.status == ProjectStatus.DRAFT
            assert project.progress == 0.0
    
    def test_project_status_update(self, app):
        """Test project status updates."""
        with app.app_context():
            user = User(email='<EMAIL>', username='testuser')
            db.session.add(user)
            db.session.commit()
            
            project = Project(user_id=user.id, title='Test Project')
            db.session.add(project)
            db.session.commit()
            
            project.update_status(ProjectStatus.PROCESSING, 0.5)
            assert project.status == ProjectStatus.PROCESSING
            assert project.progress == 0.5


class TestVideoModel:
    """Test Video model functionality."""
    
    def test_youtube_id_extraction(self):
        """Test YouTube ID extraction from URLs."""
        test_cases = [
            ('https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ'),
            ('https://youtu.be/dQw4w9WgXcQ', 'dQw4w9WgXcQ'),
            ('https://www.youtube.com/embed/dQw4w9WgXcQ', 'dQw4w9WgXcQ'),
        ]
        
        for url, expected_id in test_cases:
            assert Video.extract_youtube_id(url) == expected_id
    
    def test_video_creation(self, app):
        """Test video creation."""
        with app.app_context():
            user = User(email='<EMAIL>', username='testuser')
            db.session.add(user)
            db.session.commit()
            
            project = Project(user_id=user.id, title='Test Project')
            db.session.add(project)
            db.session.commit()
            
            video = Video(
                project_id=project.id,
                youtube_url='https://www.youtube.com/watch?v=dQw4w9WgXcQ'
            )
            
            assert video.youtube_id == 'dQw4w9WgXcQ'
            assert video.status == VideoStatus.PENDING


class TestSegmentModel:
    """Test Segment model functionality."""
    
    def test_segment_creation(self, app):
        """Test segment creation."""
        with app.app_context():
            user = User(email='<EMAIL>', username='testuser')
            db.session.add(user)
            db.session.commit()
            
            project = Project(user_id=user.id, title='Test Project')
            db.session.add(project)
            db.session.commit()
            
            video = Video(
                project_id=project.id,
                youtube_url='https://www.youtube.com/watch?v=dQw4w9WgXcQ'
            )
            db.session.add(video)
            db.session.commit()
            
            segment = Segment(
                project_id=project.id,
                video_id=video.id,
                start_time=10.0,
                end_time=30.0
            )
            
            assert segment.duration == 20.0
            assert segment.formatted_start_time == '00:10'
            assert segment.formatted_end_time == '00:30'
    
    def test_segment_overlap(self, app):
        """Test segment overlap detection."""
        with app.app_context():
            segment1 = Segment(
                project_id=1,
                video_id=1,
                start_time=10.0,
                end_time=30.0
            )
            
            segment2 = Segment(
                project_id=1,
                video_id=1,
                start_time=25.0,
                end_time=45.0
            )
            
            segment3 = Segment(
                project_id=1,
                video_id=1,
                start_time=50.0,
                end_time=70.0
            )
            
            assert segment1.overlaps_with(segment2)
            assert not segment1.overlaps_with(segment3)


class TestAuthAPI:
    """Test authentication API endpoints."""
    
    def test_user_registration(self, client):
        """Test user registration."""
        response = client.post('/api/auth/register', json={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'TestPassword123',
            'first_name': 'Test',
            'last_name': 'User'
        })
        
        assert response.status_code == 201
        data = response.get_json()
        assert 'access_token' in data
        assert 'user' in data
        assert data['user']['email'] == '<EMAIL>'
    
    def test_user_login(self, client):
        """Test user login."""
        # First register a user
        client.post('/api/auth/register', json={
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'TestPassword123'
        })
        
        # Then login
        response = client.post('/api/auth/login', json={
            'email': '<EMAIL>',
            'password': 'TestPassword123'
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'access_token' in data
        assert 'user' in data
    
    def test_get_profile(self, client, auth_headers):
        """Test getting user profile."""
        response = client.get('/api/auth/profile', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'user' in data
        assert data['user']['email'] == '<EMAIL>'


class TestProjectAPI:
    """Test project API endpoints."""
    
    def test_create_project(self, client, auth_headers):
        """Test project creation."""
        response = client.post('/api/projects', 
                             headers=auth_headers,
                             json={
                                 'title': 'Test Project',
                                 'description': 'A test project'
                             })
        
        assert response.status_code == 201
        data = response.get_json()
        assert data['project']['title'] == 'Test Project'
    
    def test_get_projects(self, client, auth_headers):
        """Test getting projects list."""
        # Create a project first
        client.post('/api/projects',
                   headers=auth_headers,
                   json={'title': 'Test Project'})
        
        response = client.get('/api/projects', headers=auth_headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'projects' in data
        assert len(data['projects']) == 1


class TestVideoAPI:
    """Test video API endpoints."""
    
    def test_validate_youtube_url(self, client, auth_headers):
        """Test YouTube URL validation."""
        response = client.post('/api/videos/validate-url',
                             headers=auth_headers,
                             json={
                                 'youtube_url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
                             })
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['valid'] is True
        assert data['youtube_id'] == 'dQw4w9WgXcQ'
    
    def test_invalid_youtube_url(self, client, auth_headers):
        """Test invalid YouTube URL validation."""
        response = client.post('/api/videos/validate-url',
                             headers=auth_headers,
                             json={
                                 'youtube_url': 'https://example.com/not-youtube'
                             })
        
        assert response.status_code == 400


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
