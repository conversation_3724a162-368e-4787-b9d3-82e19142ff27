# UniDynamics Deployment Guide

## 🚀 Production Deployment

This guide covers deploying the complete UniDynamics platform to production with all Phase 1, 2, and 3 features.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **CPU**: 4+ cores (8+ recommended for video processing)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD (for video processing and storage)
- **Network**: High bandwidth for video downloads/uploads

### Required Software
- **Docker & Docker Compose** (recommended) OR individual services
- **PostgreSQL 13+**
- **Redis 6+**
- **FFmpeg 4.4+**
- **Python 3.9+**
- **Node.js 18+**
- **Nginx** (for reverse proxy)

## 🐳 Docker Deployment (Recommended)

### 1. Create Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: unidynamics_prod
      POSTGRES_USER: unidynamics
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Redis
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Ollama (AI Service)
  ollama:
    image: ollama/ollama:latest
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - unidynamics-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://unidynamics:${POSTGRES_PASSWORD}@postgres:5432/unidynamics_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - OLLAMA_URL=http://ollama:11434
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - YOUTUBE_CLIENT_SECRETS_FILE=/app/config/youtube_client_secrets.json
      - YOUTUBE_REDIRECT_URI=${YOUTUBE_REDIRECT_URI}
    volumes:
      - ./uploads:/app/uploads
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
      - ollama
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://unidynamics:${POSTGRES_PASSWORD}@postgres:5432/unidynamics_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - OLLAMA_URL=http://ollama:11434
    volumes:
      - ./uploads:/app/uploads
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
      - ollama
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://unidynamics:${POSTGRES_PASSWORD}@postgres:5432/unidynamics_prod
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - REACT_APP_API_BASE_URL=${API_BASE_URL}
      - REACT_APP_ENVIRONMENT=production
    restart: unless-stopped
    networks:
      - unidynamics-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - unidynamics-network

volumes:
  postgres_data:
  redis_data:
  ollama_data:

networks:
  unidynamics-network:
    driver: bridge
```

### 2. Environment Configuration

Create `.env.prod`:

```bash
# Database
POSTGRES_PASSWORD=your_secure_postgres_password

# Redis
REDIS_PASSWORD=your_secure_redis_password

# JWT
JWT_SECRET_KEY=your_very_secure_jwt_secret_key_here

# YouTube API
YOUTUBE_REDIRECT_URI=https://yourdomain.com/api/advanced/youtube/callback

# API
API_BASE_URL=https://yourdomain.com/api

# Domain
DOMAIN=yourdomain.com
```

### 3. Production Dockerfiles

**Backend Dockerfile.prod:**
```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Set environment variables
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# Expose port
EXPOSE 5000

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "300", "app:app"]
```

**Frontend Dockerfile.prod:**
```dockerfile
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code and build
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 4. Nginx Configuration

```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:5000;
    }

    upstream frontend {
        server frontend:80;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=1r/s;

    server {
        listen 80;
        server_name yourdomain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase timeouts for video processing
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        # Upload routes (special handling)
        location /api/videos {
            limit_req zone=upload burst=5 nodelay;
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Large file upload settings
            client_max_body_size 2G;
            proxy_connect_timeout 600s;
            proxy_send_timeout 600s;
            proxy_read_timeout 600s;
        }

        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Handle React Router
            try_files $uri $uri/ /index.html;
        }

        # Static files
        location /static/ {
            alias /app/uploads/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## 🔧 Manual Deployment

### 1. System Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.9 python3.9-venv python3-pip nodejs npm postgresql-13 redis-server ffmpeg nginx

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. Database Setup

```bash
# Create database and user
sudo -u postgres psql
CREATE DATABASE unidynamics_prod;
CREATE USER unidynamics WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE unidynamics_prod TO unidynamics;
\q
```

### 3. Backend Deployment

```bash
# Create application directory
sudo mkdir -p /opt/unidynamics
sudo chown $USER:$USER /opt/unidynamics
cd /opt/unidynamics

# Clone repository
git clone https://github.com/yourusername/unidynamics.git .

# Setup Python environment
cd backend
python3.9 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Set environment variables
cp .env.example .env.prod
# Edit .env.prod with production values

# Run database migrations
flask db upgrade

# Create systemd service
sudo tee /etc/systemd/system/unidynamics-api.service > /dev/null <<EOF
[Unit]
Description=UniDynamics API
After=network.target

[Service]
Type=exec
User=unidynamics
Group=unidynamics
WorkingDirectory=/opt/unidynamics/backend
Environment=PATH=/opt/unidynamics/backend/venv/bin
ExecStart=/opt/unidynamics/backend/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 app:app
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Start services
sudo systemctl enable unidynamics-api
sudo systemctl start unidynamics-api
```

### 4. Frontend Deployment

```bash
# Build frontend
cd /opt/unidynamics/frontend
npm ci
npm run build

# Copy to nginx directory
sudo cp -r build/* /var/www/html/
```

### 5. Celery Setup

```bash
# Create Celery worker service
sudo tee /etc/systemd/system/unidynamics-worker.service > /dev/null <<EOF
[Unit]
Description=UniDynamics Celery Worker
After=network.target

[Service]
Type=exec
User=unidynamics
Group=unidynamics
WorkingDirectory=/opt/unidynamics/backend
Environment=PATH=/opt/unidynamics/backend/venv/bin
ExecStart=/opt/unidynamics/backend/venv/bin/celery -A app.celery worker --loglevel=info
Restart=always

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable unidynamics-worker
sudo systemctl start unidynamics-worker
```

## 🔒 Security Configuration

### 1. Firewall Setup

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL Certificate

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com
```

### 3. Security Hardening

```bash
# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# Setup fail2ban
sudo apt install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 📊 Monitoring & Logging

### 1. Application Monitoring

```bash
# Install monitoring tools
pip install prometheus-flask-exporter

# Setup log rotation
sudo tee /etc/logrotate.d/unidynamics > /dev/null <<EOF
/opt/unidynamics/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 unidynamics unidynamics
}
EOF
```

### 2. Health Checks

```bash
# Create health check script
sudo tee /opt/unidynamics/health-check.sh > /dev/null <<'EOF'
#!/bin/bash
curl -f http://localhost:5000/health || exit 1
EOF

sudo chmod +x /opt/unidynamics/health-check.sh
```

## 🚀 Deployment Commands

### Docker Deployment
```bash
# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Update deployment
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment
```bash
# Start all services
sudo systemctl start postgresql redis-server nginx
sudo systemctl start unidynamics-api unidynamics-worker

# Check status
sudo systemctl status unidynamics-api unidynamics-worker

# View logs
sudo journalctl -u unidynamics-api -f
```

## 🔄 Backup & Recovery

### Database Backup
```bash
# Create backup script
sudo tee /opt/unidynamics/backup.sh > /dev/null <<'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U unidynamics unidynamics_prod > /opt/backups/db_backup_$DATE.sql
find /opt/backups -name "db_backup_*.sql" -mtime +7 -delete
EOF

# Schedule daily backups
echo "0 2 * * * /opt/unidynamics/backup.sh" | sudo crontab -
```

## ✅ Post-Deployment Checklist

- [ ] All services running and healthy
- [ ] SSL certificate installed and working
- [ ] Database migrations completed
- [ ] Ollama model downloaded and working
- [ ] YouTube OAuth configured
- [ ] File uploads working
- [ ] Video processing pipeline functional
- [ ] Monitoring and logging configured
- [ ] Backup system in place
- [ ] Security hardening completed
- [ ] Performance testing completed

## 🆘 Troubleshooting

### Common Issues

1. **Video processing fails**
   - Check FFmpeg installation
   - Verify disk space
   - Check Celery worker logs

2. **AI analysis not working**
   - Verify Ollama is running
   - Check model is downloaded
   - Review Ollama logs

3. **YouTube upload fails**
   - Verify OAuth credentials
   - Check API quotas
   - Review YouTube API logs

4. **Database connection issues**
   - Check PostgreSQL status
   - Verify connection string
   - Check firewall rules

The UniDynamics platform is now ready for production use with all advanced features enabled!
