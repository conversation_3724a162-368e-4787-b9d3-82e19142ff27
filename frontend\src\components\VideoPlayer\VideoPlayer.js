import React, { useRef, useEffect, useState } from 'react';
import { Box, Typography, Alert } from '@mui/material';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

const VideoPlayer = ({ 
  src, 
  onTimeUpdate, 
  onLoadedMetadata, 
  currentTime, 
  segments = [],
  selectedSegments = [],
  onSegmentClick 
}) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!videoRef.current) return;

    // Initialize Video.js player
    const player = videojs(videoRef.current, {
      controls: true,
      responsive: true,
      fluid: true,
      playbackRates: [0.5, 1, 1.25, 1.5, 2],
      plugins: {
        hotkeys: {
          volumeStep: 0.1,
          seekStep: 5,
          enableModifiersForNumbers: false
        }
      }
    });

    playerRef.current = player;

    // Set up event listeners
    player.on('timeupdate', () => {
      if (onTimeUpdate) {
        onTimeUpdate(player.currentTime());
      }
    });

    player.on('loadedmetadata', () => {
      if (onLoadedMetadata) {
        onLoadedMetadata({
          duration: player.duration(),
          videoWidth: player.videoWidth(),
          videoHeight: player.videoHeight()
        });
      }
    });

    player.on('error', (e) => {
      console.error('Video player error:', e);
      setError('Failed to load video. Please check the video file.');
    });

    // Cleanup function
    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [onTimeUpdate, onLoadedMetadata]);

  // Update video source when src changes
  useEffect(() => {
    if (playerRef.current && src) {
      playerRef.current.src({ src, type: 'video/mp4' });
      setError(null);
    }
  }, [src]);

  // Seek to specific time when currentTime prop changes
  useEffect(() => {
    if (playerRef.current && typeof currentTime === 'number') {
      playerRef.current.currentTime(currentTime);
    }
  }, [currentTime]);

  const handleSegmentOverlayClick = (segment) => {
    if (onSegmentClick) {
      onSegmentClick(segment);
    }
    if (playerRef.current) {
      playerRef.current.currentTime(segment.start_time);
    }
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!src) {
    return (
      <Box 
        sx={{ 
          height: 400, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          bgcolor: 'grey.100',
          borderRadius: 1
        }}
      >
        <Typography variant="body1" color="text.secondary">
          No video loaded
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative', mb: 2 }}>
      <video
        ref={videoRef}
        className="video-js vjs-default-skin"
        data-setup="{}"
        style={{ width: '100%', height: 'auto' }}
      />
      
      {/* Segment overlays */}
      {segments.length > 0 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 40,
            left: 0,
            right: 0,
            height: 8,
            bgcolor: 'rgba(0,0,0,0.3)',
            borderRadius: 1,
            mx: 1
          }}
        >
          {segments.map((segment, index) => {
            const isSelected = selectedSegments.some(s => s.id === segment.id);
            const duration = playerRef.current?.duration() || 1;
            const left = (segment.start_time / duration) * 100;
            const width = ((segment.end_time - segment.start_time) / duration) * 100;
            
            return (
              <Box
                key={segment.id || index}
                onClick={() => handleSegmentOverlayClick(segment)}
                sx={{
                  position: 'absolute',
                  left: `${left}%`,
                  width: `${width}%`,
                  height: '100%',
                  bgcolor: isSelected ? 'primary.main' : 'warning.main',
                  opacity: isSelected ? 0.8 : 0.6,
                  cursor: 'pointer',
                  borderRadius: 0.5,
                  '&:hover': {
                    opacity: 1
                  }
                }}
                title={`${segment.title || 'Segment'} (${segment.start_time}s - ${segment.end_time}s)`}
              />
            );
          })}
        </Box>
      )}
    </Box>
  );
};

export default VideoPlayer;
