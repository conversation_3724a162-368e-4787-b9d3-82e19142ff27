import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControlLabel,
  Checkbox,
  Chip,
  Alert,
  Snackbar,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Refresh as RefreshIcon,
  Copy as CopyIcon,
  Edit as EditIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import api from '../../services/api';

const AIMetadataGenerator = ({ project, onProjectUpdate }) => {
  const [metadata, setMetadata] = useState({
    ai_title: '',
    ai_description: '',
    ai_tags: []
  });
  const [editMode, setEditMode] = useState({
    title: false,
    description: false,
    tags: false
  });
  const [generateOptions, setGenerateOptions] = useState({
    title: true,
    description: true,
    tags: true
  });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    if (project) {
      setMetadata({
        ai_title: project.ai_title || '',
        ai_description: project.ai_description || '',
        ai_tags: project.ai_tags || []
      });
    }
  }, [project]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleGenerateMetadata = async () => {
    try {
      setLoading(true);
      
      const options = Object.keys(generateOptions).filter(key => generateOptions[key]);
      
      const response = await api.post(`/advanced/projects/${project.id}/ai-metadata`, {
        options
      });
      
      showSnackbar('AI metadata generation started! This may take a few minutes.', 'info');
      
      // Poll for completion
      pollMetadataGeneration(response.data.task_id);
      
    } catch (error) {
      showSnackbar(error.response?.data?.error || 'Failed to generate AI metadata', 'error');
      setLoading(false);
    }
  };

  const pollMetadataGeneration = async (taskId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await api.get(`/advanced/projects/${project.id}/ai-metadata`);
        
        setMetadata({
          ai_title: response.data.ai_title || '',
          ai_description: response.data.ai_description || '',
          ai_tags: response.data.ai_tags || []
        });
        
        if (onProjectUpdate) {
          onProjectUpdate({ ...project, ...response.data });
        }
        
        clearInterval(pollInterval);
        setLoading(false);
        showSnackbar('AI metadata generated successfully!', 'success');
        
      } catch (error) {
        // Continue polling if not ready yet
        console.log('Still generating...');
      }
    }, 3000);
    
    // Stop polling after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setLoading(false);
    }, 300000);
  };

  const handleSaveMetadata = async () => {
    try {
      const response = await api.put(`/advanced/projects/${project.id}/ai-metadata`, metadata);
      
      if (onProjectUpdate) {
        onProjectUpdate(response.data.project);
      }
      
      setEditMode({ title: false, description: false, tags: false });
      showSnackbar('Metadata saved successfully!', 'success');
      
    } catch (error) {
      showSnackbar(error.response?.data?.error || 'Failed to save metadata', 'error');
    }
  };

  const handleCopyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      showSnackbar('Copied to clipboard!', 'success');
    } catch (error) {
      showSnackbar('Failed to copy to clipboard', 'error');
    }
  };

  const handleTagAdd = (newTag) => {
    if (newTag && !metadata.ai_tags.includes(newTag)) {
      setMetadata(prev => ({
        ...prev,
        ai_tags: [...prev.ai_tags, newTag]
      }));
    }
  };

  const handleTagRemove = (tagToRemove) => {
    setMetadata(prev => ({
      ...prev,
      ai_tags: prev.ai_tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const hasUnsavedChanges = () => {
    return (
      metadata.ai_title !== (project?.ai_title || '') ||
      metadata.ai_description !== (project?.ai_description || '') ||
      JSON.stringify(metadata.ai_tags) !== JSON.stringify(project?.ai_tags || [])
    );
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AIIcon color="primary" />
          <Typography variant="h6" component="h3">
            AI-Generated Metadata
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {hasUnsavedChanges() && (
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={handleSaveMetadata}
            >
              Save Changes
            </Button>
          )}
          
          <Button
            variant="contained"
            startIcon={loading ? <CircularProgress size={20} /> : <AIIcon />}
            onClick={handleGenerateMetadata}
            disabled={loading}
          >
            {loading ? 'Generating...' : 'Generate AI Metadata'}
          </Button>
        </Box>
      </Box>

      {/* Generation Options */}
      <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          Generate Options:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={generateOptions.title}
                onChange={(e) => setGenerateOptions(prev => ({ ...prev, title: e.target.checked }))}
              />
            }
            label="Title"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={generateOptions.description}
                onChange={(e) => setGenerateOptions(prev => ({ ...prev, description: e.target.checked }))}
              />
            }
            label="Description"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={generateOptions.tags}
                onChange={(e) => setGenerateOptions(prev => ({ ...prev, tags: e.target.checked }))}
              />
            }
            label="Tags"
          />
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* AI Title */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              AI-Generated Title
            </Typography>
            <Box>
              <Tooltip title="Copy title">
                <IconButton 
                  size="small" 
                  onClick={() => handleCopyToClipboard(metadata.ai_title)}
                  disabled={!metadata.ai_title}
                >
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit title">
                <IconButton 
                  size="small" 
                  onClick={() => setEditMode(prev => ({ ...prev, title: !prev.title }))}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {editMode.title ? (
            <TextField
              fullWidth
              value={metadata.ai_title}
              onChange={(e) => setMetadata(prev => ({ ...prev, ai_title: e.target.value }))}
              placeholder="AI-generated title will appear here..."
              helperText={`${metadata.ai_title.length}/60 characters`}
              inputProps={{ maxLength: 60 }}
            />
          ) : (
            <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, minHeight: 56 }}>
              <Typography variant="body1">
                {metadata.ai_title || 'No AI title generated yet'}
              </Typography>
            </Box>
          )}
        </Grid>

        {/* AI Description */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              AI-Generated Description
            </Typography>
            <Box>
              <Tooltip title="Copy description">
                <IconButton 
                  size="small" 
                  onClick={() => handleCopyToClipboard(metadata.ai_description)}
                  disabled={!metadata.ai_description}
                >
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit description">
                <IconButton 
                  size="small" 
                  onClick={() => setEditMode(prev => ({ ...prev, description: !prev.description }))}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {editMode.description ? (
            <TextField
              fullWidth
              multiline
              rows={6}
              value={metadata.ai_description}
              onChange={(e) => setMetadata(prev => ({ ...prev, ai_description: e.target.value }))}
              placeholder="AI-generated description will appear here..."
            />
          ) : (
            <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, minHeight: 120 }}>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {metadata.ai_description || 'No AI description generated yet'}
              </Typography>
            </Box>
          )}
        </Grid>

        {/* AI Tags */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              AI-Generated Tags
            </Typography>
            <Box>
              <Tooltip title="Copy tags">
                <IconButton 
                  size="small" 
                  onClick={() => handleCopyToClipboard(metadata.ai_tags.join(', '))}
                  disabled={metadata.ai_tags.length === 0}
                >
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit tags">
                <IconButton 
                  size="small" 
                  onClick={() => setEditMode(prev => ({ ...prev, tags: !prev.tags }))}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {editMode.tags ? (
            <Box>
              <TextField
                fullWidth
                placeholder="Add a tag and press Enter"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleTagAdd(e.target.value.trim());
                    e.target.value = '';
                  }
                }}
                sx={{ mb: 1 }}
              />
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {metadata.ai_tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    onDelete={() => handleTagRemove(tag)}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          ) : (
            <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, minHeight: 56 }}>
              {metadata.ai_tags.length > 0 ? (
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  {metadata.ai_tags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No AI tags generated yet
                </Typography>
              )}
            </Box>
          )}
        </Grid>
      </Grid>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default AIMetadataGenerator;
