# UniDynamics Windows Setup Guide

If you're having issues with the PowerShell scripts, here's a manual setup guide for Windows.

## Prerequisites

1. **Install Docker Desktop**
   - Download from: https://www.docker.com/products/docker-desktop
   - Make sure Docker Compose is included (it usually is)

2. **Install Node.js** (optional, for local development)
   - Download from: https://nodejs.org/
   - Choose the LTS version

3. **Install Python** (optional, for local development)
   - Download from: https://www.python.org/
   - Make sure to check "Add Python to PATH" during installation

## Quick Setup (Docker Only)

1. **Open Command Prompt or PowerShell as Administrator**

2. **Navigate to the project directory**
   ```cmd
   cd C:\Users\<USER>\OneDrive\Desktop\Projects\UniDynamics
   ```

3. **Create environment files**
   ```cmd
   copy backend\.env.example backend\.env
   copy frontend\.env.example frontend\.env
   ```

4. **Navigate to docker directory**
   ```cmd
   cd docker
   ```

5. **Start all services**
   ```cmd
   docker-compose up -d
   ```

6. **Initialize the database**
   ```cmd
   docker-compose run --rm backend python run.py init-db
   ```

7. **Download AI models**
   ```cmd
   docker-compose exec ollama ollama pull llama2
   ```

8. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Manual Step-by-Step Setup

### 1. Create Project Directories
```cmd
mkdir backend\uploads
mkdir backend\logs
mkdir frontend\build
mkdir docs\api
mkdir tests\backend
mkdir tests\frontend
```

### 2. Setup Environment Files
```cmd
copy backend\.env.example backend\.env
copy frontend\.env.example frontend\.env
```

Edit these files with your configuration:
- `backend\.env` - Database, API keys, etc.
- `frontend\.env` - Frontend configuration

### 3. Start Infrastructure Services
```cmd
cd docker
docker-compose up -d postgres redis
```

Wait 10 seconds for services to start.

### 4. Initialize Database
```cmd
docker-compose run --rm backend python run.py init-db
```

### 5. Start AI Service
```cmd
docker-compose up -d ollama
```

Wait 15 seconds, then download models:
```cmd
docker-compose exec ollama ollama pull llama2
```

### 6. Choose Development Mode

#### Option A: Full Docker (Easiest)
```cmd
docker-compose up -d
```

#### Option B: Hybrid Development
Keep services running:
```cmd
docker-compose up -d postgres redis ollama
```

Setup frontend:
```cmd
cd ..\frontend
npm install
```

Setup backend:
```cmd
cd ..\backend
python -m venv venv
venv\Scripts\activate.bat
pip install -r requirements.txt
```

Start development servers (in separate terminals):
```cmd
# Terminal 1: Backend
cd backend
venv\Scripts\activate.bat
python run.py

# Terminal 2: Frontend
cd frontend
npm start

# Terminal 3: Celery Worker
cd backend
venv\Scripts\activate.bat
celery -A app.celery worker --loglevel=info
```

## Troubleshooting

### Docker Issues
- Make sure Docker Desktop is running
- Try restarting Docker Desktop
- Check if ports 3000, 5000, 5432, 6379, 11434 are available

### Permission Issues
- Run Command Prompt or PowerShell as Administrator
- Make sure Docker has permission to access your drive

### Port Conflicts
If ports are in use, you can modify `docker/docker-compose.yml`:
```yaml
ports:
  - "3001:3000"  # Change frontend port
  - "5001:5000"  # Change backend port
```

### Database Connection Issues
```cmd
# Reset database
docker-compose run --rm backend python run.py reset-db

# Check database logs
docker-compose logs postgres
```

### Ollama Issues
```cmd
# Check Ollama logs
docker-compose logs ollama

# Restart Ollama
docker-compose restart ollama

# Re-download models
docker-compose exec ollama ollama pull llama2
```

## Useful Commands

### Check Service Status
```cmd
docker-compose ps
```

### View Logs
```cmd
docker-compose logs [service-name]
# Examples:
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres
```

### Stop All Services
```cmd
docker-compose down
```

### Restart Services
```cmd
docker-compose restart
```

### Clean Reset
```cmd
docker-compose down -v
docker-compose up -d
docker-compose run --rm backend python run.py init-db
```

## Next Steps

1. **Access the application**: http://localhost:3000
2. **Create an account** and start your first project
3. **Read the documentation** in the `docs/` folder
4. **Check the API** at http://localhost:5000/health

## Getting Help

- Check the main README.md
- Look at logs: `docker-compose logs [service]`
- Create an issue on GitHub if you encounter problems
