from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
import re

from app import db
from app.models.project import Project
from app.models.video import Video, VideoStatus
from app.tasks.video_tasks import process_youtube_video

videos_bp = Blueprint('videos', __name__)


def validate_youtube_url(url):
    """Validate YouTube URL format."""
    youtube_patterns = [
        r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)',
        r'youtube\.com\/v\/([^&\n?#]+)',
    ]
    
    for pattern in youtube_patterns:
        if re.search(pattern, url):
            return True
    return False


@videos_bp.route('', methods=['POST'])
@jwt_required()
def add_video():
    """Add a YouTube video to a project."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        if not data.get('project_id') or not data.get('youtube_url'):
            return jsonify({'error': 'Project ID and YouTube URL are required'}), 400
        
        project_id = data['project_id']
        youtube_url = data['youtube_url'].strip()
        
        # Validate YouTube URL
        if not validate_youtube_url(youtube_url):
            return jsonify({'error': 'Invalid YouTube URL'}), 400
        
        # Check if project exists and belongs to user
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Extract YouTube ID
        youtube_id = Video.extract_youtube_id(youtube_url)
        if not youtube_id:
            return jsonify({'error': 'Could not extract YouTube video ID'}), 400
        
        # Check if video already exists in project
        existing_video = Video.query.filter_by(
            project_id=project_id, youtube_id=youtube_id
        ).first()
        
        if existing_video:
            return jsonify({'error': 'Video already added to project'}), 409
        
        # Create video record
        video = Video(
            project_id=project_id,
            youtube_url=youtube_url
        )
        
        db.session.add(video)
        db.session.commit()
        
        # Start processing task
        task = process_youtube_video.delay(video.id)
        
        return jsonify({
            'message': 'Video added successfully',
            'video': video.to_dict(),
            'task_id': task.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Add video error: {str(e)}")
        return jsonify({'error': 'Failed to add video'}), 500


@videos_bp.route('/<int:video_id>', methods=['GET'])
@jwt_required()
def get_video(video_id):
    """Get video details."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        return jsonify({
            'video': video.to_dict(include_transcript=True, include_segments=True)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get video error: {str(e)}")
        return jsonify({'error': 'Failed to get video'}), 500


@videos_bp.route('/<int:video_id>', methods=['DELETE'])
@jwt_required()
def delete_video(video_id):
    """Delete a video from project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        # Delete video (cascade will handle related records)
        db.session.delete(video)
        db.session.commit()
        
        return jsonify({'message': 'Video deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete video error: {str(e)}")
        return jsonify({'error': 'Failed to delete video'}), 500


@videos_bp.route('/<int:video_id>/transcript', methods=['GET'])
@jwt_required()
def get_video_transcript(video_id):
    """Get video transcript."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        if not video.transcript:
            return jsonify({'error': 'Transcript not available'}), 404
        
        return jsonify({
            'transcript': video.transcript.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get video transcript error: {str(e)}")
        return jsonify({'error': 'Failed to get transcript'}), 500


@videos_bp.route('/<int:video_id>/segments', methods=['GET'])
@jwt_required()
def get_video_segments(video_id):
    """Get video segments."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        # Filter parameters
        ai_suggested_only = request.args.get('ai_suggested_only', 'false').lower() == 'true'
        selected_only = request.args.get('selected_only', 'false').lower() == 'true'
        
        query = video.segments
        
        if ai_suggested_only:
            query = query.filter_by(is_ai_suggested=True)
        
        if selected_only:
            query = query.filter_by(is_selected=True)
        
        segments = query.order_by('start_time').all()
        
        return jsonify({
            'segments': [segment.to_dict() for segment in segments]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get video segments error: {str(e)}")
        return jsonify({'error': 'Failed to get segments'}), 500


@videos_bp.route('/<int:video_id>/status', methods=['GET'])
@jwt_required()
def get_video_status(video_id):
    """Get video processing status."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        return jsonify({
            'status': video.status.value,
            'error_message': video.error_message,
            'has_transcript': video.transcript is not None,
            'segments_count': video.segments.count(),
            'ai_suggested_segments': video.get_suggested_segments_count(),
            'selected_segments': video.get_selected_segments_count()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get video status error: {str(e)}")
        return jsonify({'error': 'Failed to get video status'}), 500


@videos_bp.route('/<int:video_id>/reprocess', methods=['POST'])
@jwt_required()
def reprocess_video(video_id):
    """Reprocess a failed video."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get video and verify ownership through project
        video = db.session.query(Video).join(Project).filter(
            Video.id == video_id,
            Project.user_id == current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': 'Video not found'}), 404
        
        if video.status not in [VideoStatus.FAILED, VideoStatus.READY]:
            return jsonify({'error': 'Video cannot be reprocessed in current state'}), 400
        
        # Reset video status
        video.update_status(VideoStatus.PENDING)
        video.error_message = None
        
        # Start processing task
        task = process_youtube_video.delay(video.id)
        
        return jsonify({
            'message': 'Video reprocessing started',
            'task_id': task.id
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Reprocess video error: {str(e)}")
        return jsonify({'error': 'Failed to reprocess video'}), 500


@videos_bp.route('/validate-url', methods=['POST'])
@jwt_required()
def validate_url():
    """Validate YouTube URL without adding to project."""
    try:
        data = request.get_json()
        
        if not data.get('youtube_url'):
            return jsonify({'error': 'YouTube URL is required'}), 400
        
        youtube_url = data['youtube_url'].strip()
        
        # Validate URL format
        if not validate_youtube_url(youtube_url):
            return jsonify({'error': 'Invalid YouTube URL format'}), 400
        
        # Extract YouTube ID
        youtube_id = Video.extract_youtube_id(youtube_url)
        if not youtube_id:
            return jsonify({'error': 'Could not extract YouTube video ID'}), 400
        
        return jsonify({
            'valid': True,
            'youtube_id': youtube_id,
            'message': 'Valid YouTube URL'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Validate URL error: {str(e)}")
        return jsonify({'error': 'Failed to validate URL'}), 500
