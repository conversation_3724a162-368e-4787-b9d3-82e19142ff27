@echo off
REM UniDynamics Setup Script for Windows
REM This script sets up the development environment for UniDynamics

echo Setting up UniDynamics Development Environment
echo ==================================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed. Please install Docker Desktop first.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)
echo Docker is installed

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose is not available. Please install Docker Desktop with Compose.
    pause
    exit /b 1
)
echo Docker Compose is available

echo.
echo Setting up project structure...

REM Create necessary directories
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "backend\logs" mkdir "backend\logs"
if not exist "frontend\build" mkdir "frontend\build"
if not exist "docs\api" mkdir "docs\api"
if not exist "tests\backend" mkdir "tests\backend"
if not exist "tests\frontend" mkdir "tests\frontend"

echo Project directories created

echo.
echo Setting up environment files...

REM Backend environment file
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo Backend .env file created from example
    echo Please edit backend\.env with your configuration
) else (
    echo Backend .env file already exists
)

REM Frontend environment file
if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env" >nul
    echo Frontend .env file created from example
    echo Please edit frontend\.env with your configuration
) else (
    echo Frontend .env file already exists
)

echo.
echo Setting up Docker environment...

REM Change to docker directory
cd docker

REM Build and start infrastructure services
echo Starting PostgreSQL and Redis...
docker-compose up -d postgres redis

echo Waiting for database to be ready...
timeout /t 10 /nobreak >nul

REM Initialize database
echo Initializing database...
docker-compose run --rm backend python run.py init-db

echo Database initialized

echo.
echo Setting up Ollama (AI Models)...

REM Start Ollama
echo Starting Ollama...
docker-compose up -d ollama

echo Waiting for Ollama to be ready...
timeout /t 15 /nobreak >nul

REM Pull required models
echo Downloading AI models (this may take a while)...
docker-compose exec ollama ollama pull llama2

echo AI models downloaded

echo.
echo Setup Options:
echo.
echo 1. Full Docker Development (Recommended for beginners)
echo    - Everything runs in Docker containers
echo    - Run: docker-compose up
echo    - Access: http://localhost:3000
echo.
echo 2. Hybrid Development (Recommended for developers)
echo    - Database and services in Docker
echo    - Frontend and backend run locally
echo.

set /p choice="Choose setup option (1 or 2): "

if "%choice%"=="1" (
    echo Starting full Docker environment...
    docker-compose up -d
    echo.
    echo UniDynamics is running!
    echo Frontend: http://localhost:3000
    echo Backend API: http://localhost:5000
    echo Stop with: docker-compose down
) else if "%choice%"=="2" (
    echo Setting up hybrid development...
    
    REM Keep only infrastructure services running
    docker-compose up -d postgres redis ollama
    
    echo.
    echo Installing frontend dependencies...
    cd ..\frontend
    call npm install
    
    echo.
    echo Setting up backend virtual environment...
    cd ..\backend
    python -m venv venv
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    
    echo.
    echo Hybrid setup complete!
    echo.
    echo To start development:
    echo 1. Backend: cd backend ^&^& venv\Scripts\activate.bat ^&^& python run.py
    echo 2. Frontend: cd frontend ^&^& npm start
    echo 3. Celery Worker: cd backend ^&^& venv\Scripts\activate.bat ^&^& celery -A app.celery worker --loglevel=info
) else (
    echo Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo Setup complete!
echo.
echo Next steps:
echo 1. Read the README.md for detailed instructions
echo 2. Check the documentation in docs/
echo 3. Create your first project at http://localhost:3000
echo.
echo Need help?
echo - Check logs: docker-compose logs [service-name]
echo - Restart services: docker-compose restart
echo - Reset database: docker-compose run --rm backend python run.py reset-db
echo.
echo Happy coding!
pause
