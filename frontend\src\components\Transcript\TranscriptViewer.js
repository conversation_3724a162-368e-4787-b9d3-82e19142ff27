import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  PlayArrow as PlayIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

const TranscriptViewer = ({
  transcript,
  currentTime = 0,
  onSeek,
  segments = [],
  selectedSegments = [],
  onSegmentToggle,
  height = 400
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedSegments, setHighlightedSegments] = useState(new Set());
  const containerRef = useRef(null);
  const activeLineRef = useRef(null);

  // Parse transcript data
  const transcriptLines = React.useMemo(() => {
    if (!transcript) return [];
    
    if (typeof transcript === 'string') {
      // Simple string transcript - split by sentences
      return transcript.split(/[.!?]+/).map((text, index) => ({
        id: index,
        start_time: index * 5, // Estimate 5 seconds per sentence
        end_time: (index + 1) * 5,
        text: text.trim()
      })).filter(line => line.text);
    }
    
    if (Array.isArray(transcript)) {
      return transcript;
    }
    
    if (transcript.segments) {
      return transcript.segments;
    }
    
    return [];
  }, [transcript]);

  // Filter transcript lines based on search
  const filteredLines = React.useMemo(() => {
    if (!searchTerm) return transcriptLines;
    
    return transcriptLines.filter(line =>
      line.text.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [transcriptLines, searchTerm]);

  // Find current active line based on playback time
  const activeLineIndex = React.useMemo(() => {
    return filteredLines.findIndex(line =>
      currentTime >= line.start_time && currentTime <= line.end_time
    );
  }, [filteredLines, currentTime]);

  // Auto-scroll to active line
  useEffect(() => {
    if (activeLineRef.current && containerRef.current) {
      activeLineRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }, [activeLineIndex]);

  // Check if a line is part of any segment
  const getLineSegmentInfo = (line) => {
    const relatedSegments = segments.filter(segment =>
      line.start_time >= segment.start_time && line.end_time <= segment.end_time
    );
    
    return {
      segments: relatedSegments,
      isInSelectedSegment: relatedSegments.some(segment =>
        selectedSegments.some(selected => selected.id === segment.id)
      ),
      isInAnySegment: relatedSegments.length > 0
    };
  };

  const handleLineClick = (line) => {
    if (onSeek) {
      onSeek(line.start_time);
    }
  };

  const handleSegmentChipClick = (segment, event) => {
    event.stopPropagation();
    if (onSegmentToggle) {
      onSegmentToggle(segment);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  if (!transcript) {
    return (
      <Paper sx={{ p: 3, height, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No transcript available
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ height, display: 'flex', flexDirection: 'column' }}>
      {/* Search Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <TextField
          fullWidth
          size="small"
          placeholder="Search transcript..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            endAdornment: searchTerm && (
              <IconButton size="small" onClick={clearSearch}>
                <ClearIcon />
              </IconButton>
            )
          }}
        />
        {searchTerm && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {filteredLines.length} result(s) found
          </Typography>
        )}
      </Box>

      {/* Transcript Content */}
      <Box
        ref={containerRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2
        }}
      >
        {filteredLines.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 4 }}>
            {searchTerm ? 'No matching text found' : 'No transcript content available'}
          </Typography>
        ) : (
          filteredLines.map((line, index) => {
            const isActive = index === activeLineIndex;
            const segmentInfo = getLineSegmentInfo(line);
            
            return (
              <Box
                key={line.id || index}
                ref={isActive ? activeLineRef : null}
                onClick={() => handleLineClick(line)}
                sx={{
                  p: 1.5,
                  mb: 1,
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: isActive ? 'primary.light' : 
                           segmentInfo.isInSelectedSegment ? 'success.light' :
                           segmentInfo.isInAnySegment ? 'warning.light' : 'transparent',
                  color: isActive ? 'primary.contrastText' : 'text.primary',
                  border: isActive ? 2 : 1,
                  borderColor: isActive ? 'primary.main' : 'transparent',
                  '&:hover': {
                    bgcolor: isActive ? 'primary.light' : 'action.hover'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                {/* Timestamp and Play Button */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <Tooltip title="Play from this point">
                    <IconButton size="small" sx={{ mr: 1, p: 0.5 }}>
                      <PlayIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Typography variant="caption" color="text.secondary">
                    {formatTime(line.start_time)}
                  </Typography>
                  
                  {/* Segment chips */}
                  {segmentInfo.segments.map((segment) => {
                    const isSelected = selectedSegments.some(s => s.id === segment.id);
                    return (
                      <Chip
                        key={segment.id}
                        label={segment.title || `Segment ${segment.id}`}
                        size="small"
                        color={isSelected ? 'primary' : 'default'}
                        variant={isSelected ? 'filled' : 'outlined'}
                        onClick={(e) => handleSegmentChipClick(segment, e)}
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    );
                  })}
                </Box>

                {/* Transcript Text */}
                <Typography
                  variant="body2"
                  sx={{
                    lineHeight: 1.6,
                    fontWeight: isActive ? 500 : 400
                  }}
                >
                  {searchTerm ? (
                    line.text.split(new RegExp(`(${searchTerm})`, 'gi')).map((part, i) =>
                      part.toLowerCase() === searchTerm.toLowerCase() ? (
                        <Box
                          key={i}
                          component="span"
                          sx={{ bgcolor: 'yellow', fontWeight: 'bold' }}
                        >
                          {part}
                        </Box>
                      ) : (
                        part
                      )
                    )
                  ) : (
                    line.text
                  )}
                </Typography>
              </Box>
            );
          })
        )}
      </Box>
    </Paper>
  );
};

export default TranscriptViewer;
