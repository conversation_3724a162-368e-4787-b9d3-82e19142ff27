import os
import tempfile
import subprocess
from celery import current_task
from flask import current_app

from app import db, celery
from app.models.video import Video, VideoStatus
from app.models.project import Project, ProjectStatus
from app.models.transcript import Transcript
from app.models.segment import Segment
from app.services.youtube_service import YouTubeService
from app.services.transcription_service import TranscriptionService
from app.services.ai_service import AIService
from app.services.storage_service import StorageService
from app.services.multi_video_service import MultiVideoService
from app.services.advanced_ai_service import AdvancedAIService
from app.services.youtube_upload_service import YouTubeUploadService


@celery.task(bind=True)
def process_youtube_video(self, video_id):
    """Process a YouTube video: download, transcribe, and analyze."""
    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': 'Starting...'})
        
        # Get video record
        video = Video.query.get(video_id)
        if not video:
            raise Exception(f"Video {video_id} not found")
        
        # Initialize services
        youtube_service = YouTubeService()
        transcription_service = TranscriptionService()
        ai_service = AIService()
        storage_service = StorageService()
        
        # Step 1: Get video metadata (10%)
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': 'Getting video metadata...'})
        video.update_status(VideoStatus.DOWNLOADING)
        
        try:
            metadata = youtube_service.get_video_metadata(video.youtube_id)
            video.set_metadata(metadata)
            
            # Check if video is republishable
            is_republishable, license_type = youtube_service.is_video_republishable(metadata)
            if not is_republishable:
                video.update_status(VideoStatus.FAILED, "Video is not licensed for republishing")
                return {'error': 'Video is not licensed for republishing'}
                
        except Exception as e:
            video.update_status(VideoStatus.FAILED, f"Failed to get metadata: {str(e)}")
            raise
        
        # Step 2: Download video (30%)
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': 'Downloading video...'})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                download_info = youtube_service.download_video(video.youtube_url, temp_dir)
                local_file_path = download_info['filepath']
                
                # Upload to cloud storage
                cloud_url = storage_service.upload_file(
                    local_file_path, 
                    f"videos/{video.youtube_id}.{download_info['format']}"
                )
                
                video.set_file_info(
                    cloud_url,
                    download_info['file_size'],
                    download_info['format'],
                    download_info['resolution']
                )
                video.update_status(VideoStatus.DOWNLOADED)
                
            except Exception as e:
                video.update_status(VideoStatus.FAILED, f"Failed to download: {str(e)}")
                raise
            
            # Step 3: Transcribe video (60%)
            self.update_state(state='PROGRESS', meta={'current': 60, 'total': 100, 'status': 'Transcribing video...'})
            video.update_status(VideoStatus.TRANSCRIBING)
            
            try:
                transcript_result = transcription_service.transcribe_video(local_file_path)
                
                # Create transcript record
                transcript = Transcript(
                    video_id=video.id,
                    full_text=transcript_result['full_text'],
                    segments_data=transcript_result['segments'],
                    language=transcript_result['language'],
                    processing_time=transcript_result['processing_time'],
                    whisper_model=transcript_result['model']
                )
                
                db.session.add(transcript)
                db.session.commit()
                
                video.update_status(VideoStatus.TRANSCRIBED)
                
            except Exception as e:
                video.update_status(VideoStatus.FAILED, f"Failed to transcribe: {str(e)}")
                raise
            
            # Step 4: AI analysis for segments (90%)
            self.update_state(state='PROGRESS', meta={'current': 90, 'total': 100, 'status': 'Analyzing content...'})
            video.update_status(VideoStatus.ANALYZING)
            
            try:
                # Generate AI-suggested segments
                suggested_segments = ai_service.analyze_transcript_for_segments(
                    transcript_result['full_text'],
                    video.duration or 0
                )
                
                # Create segment records
                for segment_data in suggested_segments:
                    # Get transcript text for this segment
                    segment_transcript = transcript.get_text_at_time(
                        segment_data['start_time'],
                        segment_data['end_time']
                    )
                    
                    segment = Segment(
                        project_id=video.project_id,
                        video_id=video.id,
                        start_time=segment_data['start_time'],
                        end_time=segment_data['end_time'],
                        title=segment_data.get('title', ''),
                        description=segment_data.get('description', ''),
                        transcript_text=segment_transcript
                    )
                    
                    segment.set_ai_analysis(
                        confidence=0.8,  # Default confidence
                        reasoning=segment_data.get('description', ''),
                        topics=segment_data.get('topics', []),
                        importance_score=segment_data.get('importance_score', 0.5)
                    )
                    
                    db.session.add(segment)
                
                db.session.commit()
                video.update_status(VideoStatus.READY)
                
            except Exception as e:
                current_app.logger.warning(f"AI analysis failed, but video is still usable: {e}")
                video.update_status(VideoStatus.READY)  # Video is still usable without AI segments
        
        # Step 5: Complete (100%)
        self.update_state(state='PROGRESS', meta={'current': 100, 'total': 100, 'status': 'Complete'})
        
        return {
            'video_id': video.id,
            'status': video.status.value,
            'segments_count': video.segments.count()
        }
        
    except Exception as e:
        current_app.logger.error(f"Error processing video {video_id}: {e}")
        
        # Update video status if it exists
        video = Video.query.get(video_id)
        if video:
            video.update_status(VideoStatus.FAILED, str(e))
        
        raise


@celery.task(bind=True)
def render_final_video(self, project_id):
    """Render final video from selected segments."""
    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': 'Starting render...'})
        
        # Get project
        project = Project.query.get(project_id)
        if not project:
            raise Exception(f"Project {project_id} not found")
        
        # Get selected segments ordered by timeline position
        segments = Segment.query.filter_by(
            project_id=project_id, is_selected=True
        ).order_by(Segment.timeline_position.asc()).all()
        
        if not segments:
            raise Exception("No segments selected for rendering")
        
        # Initialize services
        storage_service = StorageService()
        
        # Step 1: Prepare segment files (20%)
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': 'Preparing segments...'})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            segment_files = []
            
            for i, segment in enumerate(segments):
                # Download source video if needed
                video = segment.video
                local_video_path = os.path.join(temp_dir, f"source_{video.id}.{video.file_format}")
                
                if not os.path.exists(local_video_path):
                    storage_service.download_file(video.file_url, local_video_path)
                
                # Extract segment
                segment_file = os.path.join(temp_dir, f"segment_{i:03d}.mp4")
                self._extract_segment(local_video_path, segment_file, segment.start_time, segment.end_time)
                segment_files.append(segment_file)
            
            # Step 2: Concatenate segments (60%)
            self.update_state(state='PROGRESS', meta={'current': 60, 'total': 100, 'status': 'Concatenating segments...'})
            
            output_file = os.path.join(temp_dir, f"final_{project_id}.mp4")
            self._concatenate_videos(segment_files, output_file)
            
            # Step 3: Upload final video (80%)
            self.update_state(state='PROGRESS', meta={'current': 80, 'total': 100, 'status': 'Uploading final video...'})
            
            final_filename = f"{project.title.replace(' ', '_')}_{project_id}.mp4"
            final_url = storage_service.upload_file(output_file, f"outputs/{final_filename}")
            
            # Get video duration
            duration = self._get_video_duration(output_file)
            
            # Step 4: Update project (100%)
            self.update_state(state='PROGRESS', meta={'current': 100, 'total': 100, 'status': 'Finalizing...'})
            
            project.output_filename = final_filename
            project.output_url = final_url
            project.output_duration = duration
            project.generate_attribution()
            project.update_status(ProjectStatus.COMPLETED)
        
        return {
            'project_id': project.id,
            'output_url': project.output_url,
            'duration': project.output_duration
        }
        
    except Exception as e:
        current_app.logger.error(f"Error rendering video for project {project_id}: {e}")
        
        # Update project status
        project = Project.query.get(project_id)
        if project:
            project.update_status(ProjectStatus.FAILED)
        
        raise


@celery.task(bind=True)
def render_multi_video_task(self, project_id):
    """Render multi-video compilation with transitions and effects."""
    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': 'Starting multi-video render...'})

        # Get project
        project = Project.query.get(project_id)
        if not project:
            raise Exception(f"Project {project_id} not found")

        # Initialize multi-video service
        multi_video_service = MultiVideoService()

        # Step 1: Render multi-video (80%)
        self.update_state(state='PROGRESS', meta={'current': 80, 'total': 100, 'status': 'Rendering multi-video...'})

        final_url = multi_video_service.render_multi_video(project_id)

        # Step 2: Complete (100%)
        self.update_state(state='PROGRESS', meta={'current': 100, 'total': 100, 'status': 'Complete'})

        project.update_status(ProjectStatus.COMPLETED)

        return {
            'project_id': project.id,
            'output_url': final_url,
            'duration': project.output_duration
        }

    except Exception as e:
        current_app.logger.error(f"Error rendering multi-video for project {project_id}: {e}")

        # Update project status
        project = Project.query.get(project_id)
        if project:
            project.update_status(ProjectStatus.FAILED)

        raise


@celery.task(bind=True)
def generate_ai_metadata_task(self, project_id, options=['title', 'description', 'tags']):
    """Generate AI metadata for a project."""
    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': 'Starting AI metadata generation...'})

        # Get project
        project = Project.query.get(project_id)
        if not project:
            raise Exception(f"Project {project_id} not found")

        # Initialize AI service
        ai_service = AdvancedAIService()

        # Generate title (30%)
        if 'title' in options:
            self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': 'Generating title...'})
            ai_title = ai_service.generate_video_title(project_id)
            if ai_title:
                project.ai_title = ai_title

        # Generate description (60%)
        if 'description' in options:
            self.update_state(state='PROGRESS', meta={'current': 60, 'total': 100, 'status': 'Generating description...'})
            ai_description = ai_service.generate_video_description(project_id)
            if ai_description:
                project.ai_description = ai_description

        # Generate tags (90%)
        if 'tags' in options:
            self.update_state(state='PROGRESS', meta={'current': 90, 'total': 100, 'status': 'Generating tags...'})
            ai_tags = ai_service.generate_video_tags(project_id)
            if ai_tags:
                project.ai_tags = ai_tags

        # Save changes (100%)
        self.update_state(state='PROGRESS', meta={'current': 100, 'total': 100, 'status': 'Complete'})
        db.session.commit()

        return {
            'project_id': project.id,
            'ai_title': project.ai_title,
            'ai_description': project.ai_description,
            'ai_tags': project.ai_tags
        }

    except Exception as e:
        current_app.logger.error(f"Error generating AI metadata for project {project_id}: {e}")
        raise


@celery.task(bind=True)
def upload_to_youtube_task(self, project_id, metadata=None):
    """Upload project video to YouTube."""
    try:
        # Update task progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': 'Starting YouTube upload...'})

        # Get project
        project = Project.query.get(project_id)
        if not project:
            raise Exception(f"Project {project_id} not found")

        if not project.output_url:
            raise Exception("Project video not ready for upload")

        # Initialize YouTube service
        youtube_service = YouTubeUploadService()

        # Download video file for upload (20%)
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': 'Preparing video file...'})

        # Get local file path (assuming output_url is local for now)
        video_file_path = project.output_url.replace('file://', '') if project.output_url.startswith('file://') else project.output_url

        # Upload to YouTube (80%)
        self.update_state(state='PROGRESS', meta={'current': 80, 'total': 100, 'status': 'Uploading to YouTube...'})

        youtube_url = youtube_service.upload_video(project_id, video_file_path, metadata)

        if youtube_url:
            # Update project with YouTube URL
            project.youtube_url = youtube_url
            db.session.commit()

            # Complete (100%)
            self.update_state(state='PROGRESS', meta={'current': 100, 'total': 100, 'status': 'Upload complete'})

            return {
                'project_id': project.id,
                'youtube_url': youtube_url
            }
        else:
            raise Exception("YouTube upload failed")

    except Exception as e:
        current_app.logger.error(f"Error uploading to YouTube for project {project_id}: {e}")
        raise


def _extract_segment(input_file, output_file, start_time, end_time):
    """Extract a segment from video using FFmpeg."""
    cmd = [
        'ffmpeg',
        '-i', input_file,
        '-ss', str(start_time),
        '-t', str(end_time - start_time),
        '-c', 'copy',
        '-avoid_negative_ts', 'make_zero',
        '-y',
        output_file
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"FFmpeg segment extraction failed: {result.stderr}")


def _concatenate_videos(input_files, output_file):
    """Concatenate multiple video files using FFmpeg."""
    # Create concat file
    concat_file = output_file.replace('.mp4', '_concat.txt')

    with open(concat_file, 'w') as f:
        for file_path in input_files:
            f.write(f"file '{file_path}'\n")

    cmd = [
        'ffmpeg',
        '-f', 'concat',
        '-safe', '0',
        '-i', concat_file,
        '-c', 'copy',
        '-y',
        output_file
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"FFmpeg concatenation failed: {result.stderr}")

    # Clean up concat file
    os.remove(concat_file)


def _get_video_duration(video_file):
    """Get video duration using FFprobe."""
    cmd = [
        'ffprobe',
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        video_file
    ]

    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        import json
        data = json.loads(result.stdout)
        return float(data['format']['duration'])

    return 0.0
