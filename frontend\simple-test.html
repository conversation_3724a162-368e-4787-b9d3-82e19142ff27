<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniDynamics - Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #1976d2;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1565c0;
        }
        .endpoint-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 UniDynamics</h1>
            <p>Simple Frontend Test</p>
        </div>

        <div class="status info">
            <strong>Status:</strong> Testing connection to backend...
        </div>

        <div class="endpoint-test">
            <h3>Backend API Tests</h3>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <button onclick="testAPI()">Test API Endpoint</button>
            <div id="results"></div>
        </div>

        <div class="endpoint-test">
            <h3>Quick Setup Check</h3>
            <ul>
                <li>✅ Frontend HTML loaded</li>
                <li id="backend-status">⏳ Backend connection: Testing...</li>
                <li id="api-status">⏳ API endpoints: Testing...</li>
            </ul>
        </div>

        <div class="endpoint-test">
            <h3>Next Steps</h3>
            <ol>
                <li>Make sure backend is running on <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></li>
                <li>Test the API endpoints using the buttons above</li>
                <li>Once backend is working, install React frontend with: <code>npm install</code></li>
                <li>Start React frontend with: <code>npm start</code></li>
            </ol>
        </div>
    </div>

    <script>
        async function testHealth() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing health endpoint...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="status success">
                        <strong>✅ Health Check Successful!</strong><br>
                        Status: ${data.status}<br>
                        Message: ${data.message}
                    </div>
                `;
                
                document.getElementById('backend-status').innerHTML = '✅ Backend connection: Working';
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">
                        <strong>❌ Health Check Failed!</strong><br>
                        Error: ${error.message}<br>
                        Make sure backend is running on http://localhost:5000
                    </div>
                `;
                
                document.getElementById('backend-status').innerHTML = '❌ Backend connection: Failed';
            }
        }

        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing API endpoint...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <div class="status success">
                        <strong>✅ API Test Successful!</strong><br>
                        Status: ${data.status}<br>
                        Message: ${data.message}<br>
                        Features: ${data.features ? data.features.join(', ') : 'None'}
                    </div>
                `;
                
                document.getElementById('api-status').innerHTML = '✅ API endpoints: Working';
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">
                        <strong>❌ API Test Failed!</strong><br>
                        Error: ${error.message}<br>
                        Make sure backend is running on http://localhost:5000
                    </div>
                `;
                
                document.getElementById('api-status').innerHTML = '❌ API endpoints: Failed';
            }
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(testHealth, 1000);
        };
    </script>
</body>
</html>
