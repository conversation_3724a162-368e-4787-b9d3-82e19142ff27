from flask import Blueprint, request, jsonify, current_app, redirect
from flask_jwt_extended import jwt_required, get_jwt_identity

from app import db
from app.models.project import Project
from app.models.segment import Segment
from app.services.advanced_ai_service import AdvancedAIService
from app.services.youtube_upload_service import YouTubeUploadService
from app.tasks.video_tasks import generate_ai_metadata_task, upload_to_youtube_task

advanced_bp = Blueprint('advanced', __name__)
ai_service = AdvancedAIService()
youtube_service = YouTubeUploadService()


@advanced_bp.route('/projects/<int:project_id>/ai-metadata', methods=['POST'])
@jwt_required()
def generate_ai_metadata(project_id):
    """Generate AI-powered metadata for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        data = request.get_json() or {}
        generate_options = data.get('options', ['title', 'description', 'tags'])
        
        # Start AI metadata generation task
        task = generate_ai_metadata_task.delay(project_id, generate_options)
        
        return jsonify({
            'message': 'AI metadata generation started',
            'task_id': task.id,
            'project_id': project_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Generate AI metadata error: {str(e)}")
        return jsonify({'error': 'Failed to generate AI metadata'}), 500


@advanced_bp.route('/projects/<int:project_id>/chapters', methods=['GET'])
@jwt_required()
def get_project_chapters(project_id):
    """Get auto-generated chapters for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        chapters = ai_service.generate_chapters(project_id)
        
        return jsonify({
            'chapters': chapters,
            'total_chapters': len(chapters)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get chapters error: {str(e)}")
        return jsonify({'error': 'Failed to get chapters'}), 500


@advanced_bp.route('/segments/<int:segment_id>/text-overlays', methods=['GET'])
@jwt_required()
def get_segment_text_overlays(segment_id):
    """Get AI-generated text overlays for a segment."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get segment and verify ownership
        segment = db.session.query(Segment).join(Project).filter(
            Segment.id == segment_id,
            Project.user_id == current_user_id
        ).first()
        
        if not segment:
            return jsonify({'error': 'Segment not found'}), 404
        
        overlays = ai_service.generate_text_overlays(segment_id)
        
        return jsonify({
            'overlays': overlays,
            'segment_id': segment_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get text overlays error: {str(e)}")
        return jsonify({'error': 'Failed to get text overlays'}), 500


@advanced_bp.route('/segments/<int:segment_id>/text-overlays', methods=['PUT'])
@jwt_required()
def update_segment_text_overlays(segment_id):
    """Update text overlay settings for a segment."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Get segment and verify ownership
        segment = db.session.query(Segment).join(Project).filter(
            Segment.id == segment_id,
            Project.user_id == current_user_id
        ).first()
        
        if not segment:
            return jsonify({'error': 'Segment not found'}), 404
        
        # Update text overlay settings
        if 'text_overlay' in data:
            segment.text_overlay = data['text_overlay']
        if 'text_overlay_position' in data:
            segment.text_overlay_position = data['text_overlay_position']
        if 'text_overlay_start' in data:
            segment.text_overlay_start = data['text_overlay_start']
        if 'text_overlay_duration' in data:
            segment.text_overlay_duration = data['text_overlay_duration']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Text overlay settings updated',
            'segment': segment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update text overlays error: {str(e)}")
        return jsonify({'error': 'Failed to update text overlays'}), 500


@advanced_bp.route('/projects/<int:project_id>/content-analysis', methods=['GET'])
@jwt_required()
def analyze_project_content(project_id):
    """Analyze project content for sentiment and recommendations."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        analysis = ai_service.analyze_content_sentiment(project_id)
        
        return jsonify({
            'analysis': analysis,
            'project_id': project_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Content analysis error: {str(e)}")
        return jsonify({'error': 'Failed to analyze content'}), 500


@advanced_bp.route('/youtube/auth', methods=['GET'])
@jwt_required()
def youtube_auth():
    """Get YouTube OAuth authorization URL."""
    try:
        current_user_id = get_jwt_identity()
        
        auth_url = youtube_service.get_auth_url(current_user_id)
        if not auth_url:
            return jsonify({'error': 'Failed to generate YouTube auth URL'}), 500
        
        return jsonify({
            'auth_url': auth_url,
            'message': 'Visit the auth_url to authorize YouTube access'
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"YouTube auth error: {str(e)}")
        return jsonify({'error': 'Failed to get YouTube auth URL'}), 500


@advanced_bp.route('/youtube/callback', methods=['GET'])
def youtube_callback():
    """Handle YouTube OAuth callback."""
    try:
        code = request.args.get('code')
        state = request.args.get('state')  # Contains user_id
        
        if not code or not state:
            return jsonify({'error': 'Missing authorization code or state'}), 400
        
        user_id = int(state)
        success = youtube_service.handle_oauth_callback(user_id, code)
        
        if success:
            # Redirect to frontend with success message
            frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
            return redirect(f"{frontend_url}/dashboard?youtube_auth=success")
        else:
            return redirect(f"{frontend_url}/dashboard?youtube_auth=error")
        
    except Exception as e:
        current_app.logger.error(f"YouTube callback error: {str(e)}")
        frontend_url = current_app.config.get('FRONTEND_URL', 'http://localhost:3000')
        return redirect(f"{frontend_url}/dashboard?youtube_auth=error")


@advanced_bp.route('/projects/<int:project_id>/youtube-upload', methods=['POST'])
@jwt_required()
def upload_to_youtube(project_id):
    """Upload project video to YouTube."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if not project.output_url:
            return jsonify({'error': 'Project video not ready for upload'}), 400
        
        # Get upload metadata
        metadata = {
            'title': data.get('title', project.ai_title or project.title),
            'description': data.get('description', project.ai_description or project.description),
            'tags': data.get('tags', project.ai_tags or []),
            'privacy': data.get('privacy', 'unlisted')
        }
        
        # Start YouTube upload task
        task = upload_to_youtube_task.delay(project_id, metadata)
        
        return jsonify({
            'message': 'YouTube upload started',
            'task_id': task.id,
            'project_id': project_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"YouTube upload error: {str(e)}")
        return jsonify({'error': 'Failed to start YouTube upload'}), 500


@advanced_bp.route('/youtube/videos/<video_id>/status', methods=['GET'])
@jwt_required()
def get_youtube_video_status(video_id):
    """Get YouTube video upload status."""
    try:
        current_user_id = get_jwt_identity()
        
        status = youtube_service.get_upload_status(video_id, current_user_id)
        if not status:
            return jsonify({'error': 'Failed to get video status'}), 500
        
        return jsonify({
            'status': status,
            'video_id': video_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get YouTube status error: {str(e)}")
        return jsonify({'error': 'Failed to get video status'}), 500


@advanced_bp.route('/youtube/videos/<video_id>/privacy', methods=['PUT'])
@jwt_required()
def update_youtube_video_privacy(video_id):
    """Update YouTube video privacy status."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        privacy_status = data.get('privacy_status')
        if privacy_status not in ['private', 'unlisted', 'public']:
            return jsonify({'error': 'Invalid privacy status'}), 400
        
        success = youtube_service.update_video_privacy(
            video_id, current_user_id, privacy_status
        )
        
        if success:
            return jsonify({
                'message': 'Video privacy updated successfully',
                'video_id': video_id,
                'privacy_status': privacy_status
            }), 200
        else:
            return jsonify({'error': 'Failed to update video privacy'}), 500
        
    except Exception as e:
        current_app.logger.error(f"Update YouTube privacy error: {str(e)}")
        return jsonify({'error': 'Failed to update video privacy'}), 500


@advanced_bp.route('/projects/<int:project_id>/ai-metadata', methods=['GET'])
@jwt_required()
def get_ai_metadata(project_id):
    """Get current AI-generated metadata for a project."""
    try:
        current_user_id = get_jwt_identity()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        return jsonify({
            'ai_title': project.ai_title,
            'ai_description': project.ai_description,
            'ai_tags': project.ai_tags,
            'project_id': project_id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get AI metadata error: {str(e)}")
        return jsonify({'error': 'Failed to get AI metadata'}), 500


@advanced_bp.route('/projects/<int:project_id>/ai-metadata', methods=['PUT'])
@jwt_required()
def update_ai_metadata(project_id):
    """Update AI-generated metadata for a project."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        # Verify project ownership
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Update AI metadata
        if 'ai_title' in data:
            project.ai_title = data['ai_title']
        if 'ai_description' in data:
            project.ai_description = data['ai_description']
        if 'ai_tags' in data:
            project.ai_tags = data['ai_tags']
        
        db.session.commit()
        
        return jsonify({
            'message': 'AI metadata updated successfully',
            'project': project.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update AI metadata error: {str(e)}")
        return jsonify({'error': 'Failed to update AI metadata'}), 500
