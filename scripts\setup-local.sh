#!/bin/bash

# UniDynamics Local Development Setup Script
# This script guides you through setting up UniDynamics for local development

set -e

echo "🚀 UniDynamics Local Development Setup"
echo "======================================"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check system requirements
echo "🔍 Checking system requirements..."
echo ""

# Check Python
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_success "Python $PYTHON_VERSION found"
else
    print_error "Python 3.9+ is required"
    echo "Install from: https://www.python.org/downloads/"
    exit 1
fi

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js $NODE_VERSION found"
else
    print_error "Node.js 16+ is required"
    echo "Install from: https://nodejs.org/"
    exit 1
fi

# Check PostgreSQL
if command_exists psql; then
    print_success "PostgreSQL found"
else
    print_warning "PostgreSQL not found in PATH"
    echo "Install PostgreSQL 13+ from: https://www.postgresql.org/download/"
    echo "Or on macOS: brew install postgresql@15"
    echo "Or on Ubuntu: sudo apt-get install postgresql postgresql-contrib"
fi

# Check Redis
if command_exists redis-server; then
    print_success "Redis found"
else
    print_warning "Redis not found"
    echo "Install Redis:"
    echo "  macOS: brew install redis"
    echo "  Ubuntu: sudo apt-get install redis-server"
    echo "  Windows: Use WSL2 or download from GitHub"
fi

# Check FFmpeg
if command_exists ffmpeg; then
    print_success "FFmpeg found"
else
    print_warning "FFmpeg not found"
    echo "Install FFmpeg:"
    echo "  macOS: brew install ffmpeg"
    echo "  Ubuntu: sudo apt-get install ffmpeg"
    echo "  Windows: Download from https://ffmpeg.org/"
fi

# Check Ollama
if command_exists ollama; then
    print_success "Ollama found"
else
    print_warning "Ollama not found"
    echo "Install Ollama from: https://ollama.ai/download"
fi

echo ""
echo "📁 Setting up project structure..."

# Create necessary directories
mkdir -p backend/uploads
mkdir -p backend/logs
mkdir -p frontend/build
mkdir -p docs/api
mkdir -p tests/backend
mkdir -p tests/frontend

print_success "Project directories created"

echo ""
echo "🔧 Setting up environment files..."

# Backend environment
if [ ! -f backend/.env ]; then
    cp backend/.env.local backend/.env
    print_success "Backend .env file created"
    print_info "Please edit backend/.env with your local database credentials"
else
    print_info "Backend .env file already exists"
fi

# Frontend environment
if [ ! -f frontend/.env ]; then
    cp frontend/.env.example frontend/.env
    print_success "Frontend .env file created"
else
    print_info "Frontend .env file already exists"
fi

echo ""
echo "🐍 Setting up Python environment..."

cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_success "Python virtual environment created"
else
    print_info "Virtual environment already exists"
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
print_info "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements-minimal.txt

print_success "Python dependencies installed"

cd ..

echo ""
echo "📦 Setting up Node.js environment..."

cd frontend

# Install dependencies
print_info "Installing Node.js dependencies..."
npm install

print_success "Node.js dependencies installed"

cd ..

echo ""
echo "🗄️  Database setup..."

print_info "Please ensure PostgreSQL is running and create the database:"
echo ""
echo "1. Start PostgreSQL (if not auto-started)"
echo "2. Connect to PostgreSQL: psql -U postgres"
echo "3. Create database: CREATE DATABASE unidynamics;"
echo "4. Create user: CREATE USER unidynamics WITH PASSWORD 'unidynamics_dev';"
echo "5. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE unidynamics TO unidynamics;"
echo ""

read -p "Have you created the database? (y/n): " db_created

if [ "$db_created" = "y" ] || [ "$db_created" = "Y" ]; then
    cd backend
    source venv/bin/activate
    
    print_info "Initializing database..."
    python run.py init-db
    
    if [ $? -eq 0 ]; then
        print_success "Database initialized successfully"
    else
        print_error "Database initialization failed"
        print_info "Please check your database connection settings in backend/.env"
    fi
    
    cd ..
else
    print_warning "Please create the database manually and run: python run.py init-db"
fi

echo ""
echo "🤖 Ollama setup..."

if command_exists ollama; then
    print_info "Starting Ollama service..."
    ollama serve &
    OLLAMA_PID=$!
    
    sleep 5
    
    print_info "Downloading AI models (this may take a while)..."
    ollama pull llama2
    
    if [ $? -eq 0 ]; then
        print_success "Ollama models downloaded"
    else
        print_warning "Failed to download models. You can do this manually later with: ollama pull llama2"
    fi
    
    # Stop Ollama for now
    kill $OLLAMA_PID 2>/dev/null || true
else
    print_warning "Ollama not found. Please install from https://ollama.ai/download"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 To start development:"
echo ""
echo "1. Start required services:"
echo "   - PostgreSQL (usually auto-starts)"
echo "   - Redis: redis-server"
echo "   - Ollama: ollama serve"
echo ""
echo "2. Start application services:"
echo "   Terminal 1 - Backend API:"
echo "   cd backend && source venv/bin/activate && python run.py"
echo ""
echo "   Terminal 2 - Celery Worker:"
echo "   cd backend && source venv/bin/activate && celery -A app.celery worker --loglevel=info"
echo ""
echo "   Terminal 3 - Frontend:"
echo "   cd frontend && npm start"
echo ""
echo "3. Access the application:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:5000"
echo ""
echo "📚 Documentation:"
echo "   - Local Setup Guide: docs/LOCAL_SETUP.md"
echo "   - Development Guide: docs/DEVELOPMENT.md"
echo "   - API Documentation: docs/API.md"
echo ""
echo "🆘 Need help?"
echo "   - Check the troubleshooting section in docs/LOCAL_SETUP.md"
echo "   - Verify all services are running"
echo "   - Check logs for error messages"
echo ""
print_success "Happy coding! 🚀"
