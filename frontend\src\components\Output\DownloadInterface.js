import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Divider,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Download as DownloadIcon,
  Share as ShareIcon,
  ContentCopy as CopyIcon,
  VideoFile as VideoFileIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';

const DownloadInterface = ({ 
  project,
  downloadData,
  onDownload,
  onShare,
  loading = false 
}) => {
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [shareFormat, setShareFormat] = useState('link');
  const [customMessage, setCustomMessage] = useState('');
  const [copied, setCopied] = useState(false);

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'Unknown duration';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCopyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const generateShareContent = () => {
    const baseMessage = customMessage || `Check out this video summary I created with UniDynamics!`;
    
    switch (shareFormat) {
      case 'link':
        return downloadData?.download_url || '';
      case 'embed':
        return `<video controls width="100%">
  <source src="${downloadData?.download_url}" type="video/mp4">
  Your browser does not support the video tag.
</video>`;
      case 'social':
        return `${baseMessage}\n\n${downloadData?.download_url}`;
      default:
        return downloadData?.download_url || '';
    }
  };

  if (!downloadData) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Video Not Ready
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Complete video processing to download your summary.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6" component="h3">
          Download Your Video
        </Typography>
        <Chip 
          icon={<CheckCircleIcon />}
          label="Ready" 
          color="success" 
          variant="outlined"
        />
      </Box>

      {/* Video Information */}
      <Box sx={{ mb: 3 }}>
        <List dense>
          <ListItem>
            <ListItemIcon>
              <VideoFileIcon />
            </ListItemIcon>
            <ListItemText
              primary="Filename"
              secondary={downloadData.filename || 'video_summary.mp4'}
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary="Duration"
              secondary={formatDuration(downloadData.duration)}
            />
          </ListItem>
          
          {downloadData.file_size && (
            <ListItem>
              <ListItemIcon>
                <InfoIcon />
              </ListItemIcon>
              <ListItemText
                primary="File Size"
                secondary={formatFileSize(downloadData.file_size)}
              />
            </ListItem>
          )}
        </List>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Download Actions */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
          onClick={() => onDownload && onDownload(downloadData.download_url)}
          disabled={loading}
          sx={{ flex: 1, minWidth: 200 }}
        >
          {loading ? 'Preparing Download...' : 'Download Video'}
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<ShareIcon />}
          onClick={() => setShareDialogOpen(true)}
          sx={{ flex: 1, minWidth: 150 }}
        >
          Share
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<LaunchIcon />}
          onClick={() => window.open(downloadData.download_url, '_blank')}
        >
          Open in New Tab
        </Button>
      </Box>

      {/* Attribution Information */}
      {downloadData.attribution && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Attribution Information
          </Typography>
          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
              {downloadData.attribution}
            </Typography>
            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
              <Tooltip title={copied ? 'Copied!' : 'Copy attribution'}>
                <IconButton 
                  size="small" 
                  onClick={() => handleCopyToClipboard(downloadData.attribution)}
                >
                  <CopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Paper>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Please include this attribution when sharing or republishing the video.
          </Typography>
        </Box>
      )}

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Share Your Video</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Share Format</InputLabel>
              <Select
                value={shareFormat}
                label="Share Format"
                onChange={(e) => setShareFormat(e.target.value)}
              >
                <MenuItem value="link">Direct Link</MenuItem>
                <MenuItem value="embed">HTML Embed Code</MenuItem>
                <MenuItem value="social">Social Media Post</MenuItem>
              </Select>
            </FormControl>

            {shareFormat === 'social' && (
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Custom Message"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                placeholder="Add a custom message for your social media post..."
                sx={{ mb: 2 }}
              />
            )}

            <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                <Typography variant="subtitle2">
                  {shareFormat === 'link' && 'Share Link'}
                  {shareFormat === 'embed' && 'Embed Code'}
                  {shareFormat === 'social' && 'Social Media Post'}
                </Typography>
                <Tooltip title={copied ? 'Copied!' : 'Copy to clipboard'}>
                  <IconButton 
                    size="small" 
                    onClick={() => handleCopyToClipboard(generateShareContent())}
                  >
                    <CopyIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <Typography 
                variant="body2" 
                component="pre" 
                sx={{ 
                  whiteSpace: 'pre-wrap', 
                  wordBreak: 'break-all',
                  fontFamily: shareFormat === 'embed' ? 'monospace' : 'inherit'
                }}
              >
                {generateShareContent()}
              </Typography>
            </Paper>
          </Box>

          {shareFormat !== 'link' && (
            <Alert severity="info" sx={{ mt: 2 }}>
              {shareFormat === 'embed' && 'Copy this HTML code to embed the video in your website.'}
              {shareFormat === 'social' && 'Copy this text to share on social media platforms.'}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialogOpen(false)}>Close</Button>
          <Button 
            variant="contained" 
            onClick={() => {
              handleCopyToClipboard(generateShareContent());
              if (onShare) onShare(shareFormat, generateShareContent());
            }}
          >
            Copy & Share
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default DownloadInterface;
