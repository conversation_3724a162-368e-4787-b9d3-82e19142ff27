# Video Processing UI Implementation

This document describes the implementation of the video processing user interface for UniDynamics, which enables users to interact with video content, select segments, and create summarized videos.

## Overview

The video processing UI consists of several key components that work together to provide a complete video editing experience:

1. **VideoPlayer** - Video playback with segment visualization
2. **TranscriptViewer** - Interactive transcript with search and navigation
3. **SegmentTimeline** - Visual timeline showing video segments
4. **SegmentSelector** - Segment management and selection interface
5. **ProcessingStatus** - Real-time processing status updates
6. **DownloadInterface** - Download and sharing functionality

## Architecture

### Component Structure

```
VideoEditorPage
├── VideoPlayer
├── SegmentTimeline
├── TranscriptViewer
├── SegmentSelector
├── ProcessingStatus
└── DownloadInterface
```

### Data Flow

1. **Project Loading**: `useVideoProcessing` hook fetches project and video data
2. **Video Processing**: Backend processes videos (download, transcribe, analyze)
3. **Segment Management**: AI suggests segments, user can select/deselect
4. **Video Rendering**: Selected segments are rendered into final video
5. **Download**: User can download the completed video with attribution

## Components

### VideoPlayer

**Location**: `frontend/src/components/VideoPlayer/VideoPlayer.js`

**Purpose**: Displays video content with playback controls and segment overlays.

**Key Features**:
- Video.js integration for robust video playback
- Segment overlays showing AI-suggested and selected segments
- Click-to-seek functionality
- Responsive design

**Props**:
- `src`: Video source URL
- `segments`: Array of video segments
- `selectedSegments`: Array of selected segments
- `onTimeUpdate`: Callback for time updates
- `onSegmentClick`: Callback for segment clicks

### TranscriptViewer

**Location**: `frontend/src/components/Transcript/TranscriptViewer.js`

**Purpose**: Interactive transcript display with search and navigation.

**Key Features**:
- Full-text search functionality
- Segment highlighting and selection
- Auto-scroll to current playback position
- Click-to-seek from transcript lines

**Props**:
- `transcript`: Transcript data with timestamps
- `currentTime`: Current playback time
- `segments`: Video segments
- `onSeek`: Callback for seeking to specific time
- `onSegmentToggle`: Callback for segment selection

### SegmentTimeline

**Location**: `frontend/src/components/Timeline/SegmentTimeline.js`

**Purpose**: Visual timeline representation of video segments.

**Key Features**:
- Interactive timeline with click-to-seek
- Color-coded segments by importance
- Current time indicator
- Segment context menus for editing/deletion

**Props**:
- `duration`: Video duration in seconds
- `segments`: Array of segments
- `currentTime`: Current playback time
- `onSegmentToggle`: Segment selection callback
- `onSeek`: Seek callback

### SegmentSelector

**Location**: `frontend/src/components/Segments/SegmentSelector.js`

**Purpose**: Comprehensive segment management interface.

**Key Features**:
- Segment list with selection controls
- Search and filtering capabilities
- Bulk selection operations
- Segment details and metadata display

**Props**:
- `segments`: Array of segments
- `selectedSegments`: Selected segments
- `onSegmentToggle`: Selection callback
- `onSelectAll`: Select all callback
- `onSelectNone`: Deselect all callback

### ProcessingStatus

**Location**: `frontend/src/components/Processing/ProcessingStatus.js`

**Purpose**: Real-time status updates for video processing.

**Key Features**:
- Step-by-step processing visualization
- Progress indicators
- Error handling and retry functionality
- Expandable details view

**Props**:
- `videoId`: Video identifier
- `status`: Processing status object
- `onRetry`: Retry callback for failed processing

### DownloadInterface

**Location**: `frontend/src/components/Output/DownloadInterface.js`

**Purpose**: Download and sharing interface for completed videos.

**Key Features**:
- Download button with file information
- Share functionality (direct link, embed code, social media)
- Attribution information display
- Copy-to-clipboard functionality

**Props**:
- `downloadData`: Download information object
- `onDownload`: Download callback
- `onShare`: Share callback

## State Management

### useVideoProcessing Hook

**Location**: `frontend/src/hooks/useVideoProcessing.js`

**Purpose**: Centralized state management for video processing workflow.

**Key Features**:
- Project and video data management
- Segment selection state
- Processing status polling
- API integration for all video operations

**Returns**:
- `project`: Current project data
- `videos`: Array of project videos
- `selectedVideo`: Currently selected video
- `segments`: Video segments
- `selectedSegments`: Selected segments
- `transcript`: Video transcript
- `loading`: Loading state
- `error`: Error state
- Action functions for video operations

## API Integration

The video processing UI integrates with the following backend endpoints:

### Projects API
- `GET /api/projects/{id}` - Get project details
- `GET /api/projects/{id}/videos` - Get project videos

### Videos API
- `POST /api/videos` - Add video to project
- `GET /api/videos/{id}` - Get video details
- `GET /api/videos/{id}/status` - Get processing status
- `GET /api/videos/{id}/segments` - Get video segments

### Processing API
- `POST /api/processing/render` - Start video rendering
- `GET /api/processing/project/{id}/download` - Get download URL
- `POST /api/processing/project/{id}/regenerate-segments` - Regenerate AI segments

## User Workflow

1. **Project Creation**: User creates a new project
2. **Video Addition**: User adds YouTube video URL
3. **Processing**: System downloads, transcribes, and analyzes video
4. **Segment Review**: User reviews AI-suggested segments
5. **Segment Selection**: User selects desired segments
6. **Video Rendering**: System creates final video from selected segments
7. **Download**: User downloads completed video with attribution

## Error Handling

The UI implements comprehensive error handling:

- **Network Errors**: Retry mechanisms and user-friendly messages
- **Processing Failures**: Clear error states with retry options
- **Validation Errors**: Real-time form validation
- **Timeout Handling**: Graceful handling of long-running operations

## Performance Considerations

- **Lazy Loading**: Components load data as needed
- **Debounced Updates**: Segment selection updates are debounced
- **Efficient Rendering**: Virtual scrolling for large transcript lists
- **Memory Management**: Proper cleanup of video players and timers

## Testing

Comprehensive test suite covers:

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Component interaction and data flow
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Loading and rendering performance

**Test Location**: `tests/frontend/VideoProcessing.test.js`

## Browser Compatibility

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Video Formats**: MP4, WebM (via Video.js)
- **Features**: ES6+, Web APIs (Clipboard, Local Storage)

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and semantic HTML
- **Color Contrast**: WCAG 2.1 AA compliance
- **Focus Management**: Proper focus handling

## Future Enhancements

1. **Real-time Collaboration**: Multiple users editing simultaneously
2. **Advanced Editing**: Transitions, effects, text overlays
3. **Mobile Support**: Touch-optimized interface
4. **Offline Mode**: Local processing capabilities
5. **Batch Processing**: Multiple video processing

## Troubleshooting

### Common Issues

1. **Video Not Loading**: Check video URL and format compatibility
2. **Slow Processing**: Verify backend services are running
3. **Segment Selection Not Saving**: Check network connectivity
4. **Download Fails**: Verify file permissions and storage

### Debug Mode

Enable debug mode by setting `REACT_APP_DEBUG=true` in environment variables.

## Dependencies

- **Video.js**: Video playback and controls
- **Material-UI**: UI components and theming
- **React Router**: Navigation and routing
- **Axios**: HTTP client for API calls

## Configuration

Environment variables:
- `REACT_APP_API_BASE_URL`: Backend API URL
- `REACT_APP_DEBUG`: Enable debug mode
- `REACT_APP_MAX_VIDEO_SIZE`: Maximum video file size
