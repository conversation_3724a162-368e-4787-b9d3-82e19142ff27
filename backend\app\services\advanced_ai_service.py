import json
import requests
from typing import Dict, List, Any, Optional
from flask import current_app

from app.models.project import Project
from app.models.video import Video
from app.models.segment import Segment


class AdvancedAIService:
    """Advanced AI service for Phase 3 features including auto-titles, descriptions, and chapters."""
    
    def __init__(self):
        self.ollama_url = current_app.config.get('OLLAMA_URL', 'http://localhost:11434')
        self.model = current_app.config.get('OLLAMA_MODEL', 'llama2')
    
    def generate_video_title(self, project_id: int) -> Optional[str]:
        """Generate an AI-powered title for the video based on content analysis."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return None
            
            # Get all segments and their summaries
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).order_by(Segment.timeline_position).all()
            
            if not segments:
                return None
            
            # Prepare content for AI analysis
            content_summary = self._prepare_content_summary(segments)
            
            prompt = f"""
            Based on the following video content summary, generate a compelling and concise title for a YouTube video.
            The title should be:
            - Engaging and clickable
            - Under 60 characters
            - Descriptive of the main content
            - SEO-friendly
            
            Content Summary:
            {content_summary}
            
            Generate only the title, no additional text:
            """
            
            response = self._call_ollama(prompt)
            if response:
                title = response.strip().strip('"').strip("'")
                # Ensure title is under 60 characters
                if len(title) > 60:
                    title = title[:57] + "..."
                return title
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"Error generating video title: {str(e)}")
            return None
    
    def generate_video_description(self, project_id: int) -> Optional[str]:
        """Generate an AI-powered description for the video."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return None
            
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).order_by(Segment.timeline_position).all()
            
            if not segments:
                return None
            
            content_summary = self._prepare_content_summary(segments)
            
            # Get source video information
            source_videos = []
            for segment in segments:
                if segment.video and segment.video.youtube_url:
                    video_info = {
                        'title': segment.video.title,
                        'url': segment.video.youtube_url,
                        'channel': segment.video.channel_name
                    }
                    if video_info not in source_videos:
                        source_videos.append(video_info)
            
            prompt = f"""
            Create a comprehensive YouTube video description based on the following content.
            The description should include:
            - A compelling overview of the video content
            - Key topics covered with timestamps
            - Source attribution
            - Call to action for engagement
            
            Content Summary:
            {content_summary}
            
            Source Videos:
            {json.dumps(source_videos, indent=2)}
            
            Format the description professionally with proper sections and timestamps.
            """
            
            response = self._call_ollama(prompt)
            return response
            
        except Exception as e:
            current_app.logger.error(f"Error generating video description: {str(e)}")
            return None
    
    def generate_video_tags(self, project_id: int) -> List[str]:
        """Generate relevant tags for the video."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return []
            
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).all()
            
            if not segments:
                return []
            
            # Collect all topics from segments
            all_topics = []
            for segment in segments:
                if segment.topics:
                    all_topics.extend(segment.topics)
            
            content_summary = self._prepare_content_summary(segments)
            
            prompt = f"""
            Generate relevant YouTube tags based on the following video content.
            Return 10-15 tags that are:
            - Relevant to the content
            - Popular search terms
            - Mix of broad and specific tags
            - Good for SEO
            
            Content Summary:
            {content_summary}
            
            Existing Topics: {', '.join(set(all_topics))}
            
            Return only the tags as a comma-separated list:
            """
            
            response = self._call_ollama(prompt)
            if response:
                tags = [tag.strip() for tag in response.split(',')]
                return [tag for tag in tags if tag and len(tag) > 2][:15]
            
            return []
            
        except Exception as e:
            current_app.logger.error(f"Error generating video tags: {str(e)}")
            return []
    
    def generate_chapters(self, project_id: int) -> List[Dict[str, Any]]:
        """Generate chapter markers for the video."""
        try:
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).order_by(Segment.timeline_position).all()
            
            if not segments:
                return []
            
            chapters = []
            current_time = 0.0
            
            for i, segment in enumerate(segments):
                # Generate chapter title using AI
                chapter_title = self._generate_chapter_title(segment, i + 1)
                
                chapters.append({
                    'title': chapter_title,
                    'start_time': current_time,
                    'end_time': current_time + segment.duration,
                    'duration': segment.duration,
                    'segment_id': segment.id
                })
                
                current_time += segment.duration
            
            return chapters
            
        except Exception as e:
            current_app.logger.error(f"Error generating chapters: {str(e)}")
            return []
    
    def generate_text_overlays(self, segment_id: int) -> List[Dict[str, Any]]:
        """Generate text overlays for a segment."""
        try:
            segment = Segment.query.get(segment_id)
            if not segment:
                return []
            
            prompt = f"""
            Based on the following video segment content, suggest 2-3 text overlays that would enhance viewer understanding.
            Each overlay should be:
            - Concise (under 50 characters)
            - Informative or engaging
            - Appropriate for the content
            
            Segment Title: {segment.title}
            Segment Summary: {segment.summary}
            Duration: {segment.duration} seconds
            
            Return as JSON array with format:
            [
                {{"text": "overlay text", "start_time": 2.0, "duration": 3.0, "position": "bottom"}},
                ...
            ]
            """
            
            response = self._call_ollama(prompt)
            if response:
                try:
                    overlays = json.loads(response)
                    return overlays if isinstance(overlays, list) else []
                except json.JSONDecodeError:
                    return []
            
            return []
            
        except Exception as e:
            current_app.logger.error(f"Error generating text overlays: {str(e)}")
            return []
    
    def analyze_content_sentiment(self, project_id: int) -> Dict[str, Any]:
        """Analyze the overall sentiment and tone of the video content."""
        try:
            segments = Segment.query.filter_by(
                project_id=project_id, 
                is_selected=True
            ).all()
            
            if not segments:
                return {}
            
            content_summary = self._prepare_content_summary(segments)
            
            prompt = f"""
            Analyze the sentiment and tone of the following video content.
            Provide analysis in JSON format:
            {{
                "overall_sentiment": "positive/negative/neutral",
                "tone": "educational/entertaining/informative/promotional/etc",
                "target_audience": "description of likely audience",
                "content_type": "tutorial/review/entertainment/news/etc",
                "engagement_score": 0.0-1.0,
                "recommendations": ["suggestion1", "suggestion2"]
            }}
            
            Content Summary:
            {content_summary}
            """
            
            response = self._call_ollama(prompt)
            if response:
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    return {}
            
            return {}
            
        except Exception as e:
            current_app.logger.error(f"Error analyzing content sentiment: {str(e)}")
            return {}
    
    def _prepare_content_summary(self, segments: List[Segment]) -> str:
        """Prepare a content summary from segments."""
        summary_parts = []
        
        for i, segment in enumerate(segments):
            part = f"Segment {i + 1} ({segment.duration:.1f}s):"
            if segment.title:
                part += f" {segment.title}"
            if segment.summary:
                part += f" - {segment.summary}"
            if segment.topics:
                part += f" [Topics: {', '.join(segment.topics)}]"
            summary_parts.append(part)
        
        return "\n".join(summary_parts)
    
    def _generate_chapter_title(self, segment: Segment, chapter_number: int) -> str:
        """Generate a chapter title for a segment."""
        try:
            prompt = f"""
            Generate a concise chapter title (under 50 characters) for this video segment:
            
            Segment Title: {segment.title or f'Segment {chapter_number}'}
            Summary: {segment.summary or 'No summary available'}
            Duration: {segment.duration} seconds
            
            The title should be descriptive and engaging. Return only the title:
            """
            
            response = self._call_ollama(prompt)
            if response:
                title = response.strip().strip('"').strip("'")
                if len(title) > 50:
                    title = title[:47] + "..."
                return title
            
            return segment.title or f"Chapter {chapter_number}"
            
        except Exception as e:
            current_app.logger.error(f"Error generating chapter title: {str(e)}")
            return segment.title or f"Chapter {chapter_number}"
    
    def _call_ollama(self, prompt: str) -> Optional[str]:
        """Make a request to Ollama API."""
        try:
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "max_tokens": 1000
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '').strip()
            else:
                current_app.logger.error(f"Ollama API error: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Error calling Ollama API: {str(e)}")
            return None
        except Exception as e:
            current_app.logger.error(f"Unexpected error in Ollama call: {str(e)}")
            return None
