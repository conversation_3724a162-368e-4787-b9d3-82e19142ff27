import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Stepper,
  Step,
  Step<PERSON><PERSON><PERSON>,
  StepContent,
  Alert,
  <PERSON><PERSON>,
  <PERSON>lapse,
  IconButton,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as HourglassIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

const ProcessingStatus = ({ 
  videoId, 
  status, 
  onRetry,
  showDetails = true 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [progress, setProgress] = useState(0);

  const steps = [
    { key: 'downloading', label: 'Downloading Video', description: 'Fetching video from YouTube' },
    { key: 'transcribing', label: 'Transcribing Audio', description: 'Converting speech to text using AI' },
    { key: 'analyzing', label: 'Analyzing Content', description: 'Identifying key segments with AI' },
    { key: 'completed', label: 'Processing Complete', description: 'Ready for editing' }
  ];

  const getStepStatus = (stepKey) => {
    if (!status) return 'pending';
    
    const stepOrder = ['downloading', 'transcribing', 'analyzing', 'completed'];
    const currentStepIndex = stepOrder.indexOf(status.status);
    const stepIndex = stepOrder.indexOf(stepKey);
    
    if (status.status === 'failed') {
      return stepIndex <= currentStepIndex ? 'error' : 'pending';
    }
    
    if (stepIndex < currentStepIndex) return 'completed';
    if (stepIndex === currentStepIndex) return 'active';
    return 'pending';
  };

  const getStepIcon = (stepKey) => {
    const stepStatus = getStepStatus(stepKey);
    
    switch (stepStatus) {
      case 'completed':
        return <CheckCircleIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'active':
        return <HourglassIcon color="primary" />;
      default:
        return null;
    }
  };

  const getOverallStatus = () => {
    if (!status) return { label: 'Pending', color: 'default', severity: 'info' };
    
    switch (status.status) {
      case 'completed':
        return { label: 'Completed', color: 'success', severity: 'success' };
      case 'failed':
        return { label: 'Failed', color: 'error', severity: 'error' };
      case 'downloading':
        return { label: 'Downloading', color: 'primary', severity: 'info' };
      case 'transcribing':
        return { label: 'Transcribing', color: 'primary', severity: 'info' };
      case 'analyzing':
        return { label: 'Analyzing', color: 'primary', severity: 'info' };
      default:
        return { label: 'Processing', color: 'primary', severity: 'info' };
    }
  };

  // Simulate progress for active steps
  useEffect(() => {
    if (status?.status && ['downloading', 'transcribing', 'analyzing'].includes(status.status)) {
      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 1000);
      
      return () => clearInterval(interval);
    } else if (status?.status === 'completed') {
      setProgress(100);
    } else if (status?.status === 'failed') {
      setProgress(0);
    }
  }, [status?.status]);

  const overallStatus = getOverallStatus();
  const activeStep = steps.findIndex(step => getStepStatus(step.key) === 'active');

  if (!showDetails) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Chip 
          label={overallStatus.label}
          color={overallStatus.color}
          size="small"
          icon={getStepIcon(status?.status)}
        />
        {status?.status && ['downloading', 'transcribing', 'analyzing'].includes(status.status) && (
          <Box sx={{ width: 100 }}>
            <LinearProgress variant="determinate" value={progress} />
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h3">
          Processing Status
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip 
            label={overallStatus.label}
            color={overallStatus.color}
            variant="outlined"
          />
          <IconButton 
            size="small" 
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Progress Bar for Active Processing */}
      {status?.status && ['downloading', 'transcribing', 'analyzing'].includes(status.status) && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            {Math.round(progress)}% complete
          </Typography>
        </Box>
      )}

      {/* Error Alert */}
      {status?.status === 'failed' && (
        <Alert 
          severity="error" 
          sx={{ mb: 2 }}
          action={
            onRetry && (
              <Button 
                color="inherit" 
                size="small" 
                startIcon={<RefreshIcon />}
                onClick={() => onRetry(videoId)}
              >
                Retry
              </Button>
            )
          }
        >
          {status.error_message || 'Processing failed. Please try again.'}
        </Alert>
      )}

      {/* Success Alert */}
      {status?.status === 'completed' && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Video processing completed successfully! 
          {status.segments_count && (
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              Found {status.segments_count} segments, {status.ai_suggested_segments} AI-suggested.
            </Typography>
          )}
        </Alert>
      )}

      {/* Detailed Steps */}
      <Collapse in={expanded}>
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => {
            const stepStatus = getStepStatus(step.key);
            const isError = stepStatus === 'error';
            
            return (
              <Step key={step.key} completed={stepStatus === 'completed'}>
                <StepLabel 
                  error={isError}
                  icon={getStepIcon(step.key)}
                >
                  <Typography 
                    variant="subtitle2" 
                    color={isError ? 'error' : 'inherit'}
                  >
                    {step.label}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body2" color="text.secondary">
                    {step.description}
                  </Typography>
                  
                  {/* Step-specific information */}
                  {step.key === 'transcribing' && status?.has_transcript && (
                    <Typography variant="caption" color="success.main" sx={{ display: 'block', mt: 0.5 }}>
                      ✓ Transcript generated
                    </Typography>
                  )}
                  
                  {step.key === 'analyzing' && status?.ai_suggested_segments > 0 && (
                    <Typography variant="caption" color="success.main" sx={{ display: 'block', mt: 0.5 }}>
                      ✓ {status.ai_suggested_segments} segments identified
                    </Typography>
                  )}
                  
                  {stepStatus === 'active' && (
                    <Box sx={{ mt: 1, width: '100%' }}>
                      <LinearProgress size="small" />
                    </Box>
                  )}
                </StepContent>
              </Step>
            );
          })}
        </Stepper>
      </Collapse>

      {/* Additional Information */}
      {status && expanded && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" component="div">
            <strong>Video ID:</strong> {videoId}
          </Typography>
          {status.has_transcript !== undefined && (
            <Typography variant="caption" color="text.secondary" component="div">
              <strong>Transcript:</strong> {status.has_transcript ? 'Available' : 'Not available'}
            </Typography>
          )}
          {status.segments_count !== undefined && (
            <Typography variant="caption" color="text.secondary" component="div">
              <strong>Total Segments:</strong> {status.segments_count}
            </Typography>
          )}
          {status.ai_suggested_segments !== undefined && (
            <Typography variant="caption" color="text.secondary" component="div">
              <strong>AI Suggested:</strong> {status.ai_suggested_segments}
            </Typography>
          )}
          {status.selected_segments !== undefined && (
            <Typography variant="caption" color="text.secondary" component="div">
              <strong>Selected:</strong> {status.selected_segments}
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default ProcessingStatus;
