"""Add Phase 2 and Phase 3 fields

Revision ID: phase2_phase3_fields
Revises: initial_schema
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'phase2_phase3_fields'
down_revision = 'initial_schema'
branch_labels = None
depends_on = None


def upgrade():
    """Add Phase 2 and Phase 3 fields to existing tables."""
    
    # Add Phase 2 fields to projects table
    op.add_column('projects', sa.Column('compilation_mode', sa.String(20), default='single'))
    op.add_column('projects', sa.Column('transition_type', sa.String(20), default='cut'))
    op.add_column('projects', sa.Column('transition_duration', sa.Float, default=0.5))
    op.add_column('projects', sa.Column('intro_file_url', sa.String(500)))
    op.add_column('projects', sa.Column('outro_file_url', sa.String(500)))
    op.add_column('projects', sa.Column('background_music_url', sa.String(500)))
    op.add_column('projects', sa.Column('background_music_volume', sa.Float, default=0.2))
    
    # Add Phase 3 fields to projects table
    op.add_column('projects', sa.Column('auto_chapters', sa.Boolean, default=False))
    op.add_column('projects', sa.Column('youtube_upload_enabled', sa.Boolean, default=False))
    op.add_column('projects', sa.Column('ai_title', sa.String(200)))
    op.add_column('projects', sa.Column('ai_description', sa.Text))
    op.add_column('projects', sa.Column('ai_tags', sa.JSON))
    op.add_column('projects', sa.Column('youtube_url', sa.String(500)))
    
    # Add Phase 2 fields to segments table
    op.add_column('segments', sa.Column('custom_title', sa.String(200)))
    op.add_column('segments', sa.Column('custom_description', sa.Text))
    op.add_column('segments', sa.Column('transition_in', sa.String(20), default='cut'))
    op.add_column('segments', sa.Column('transition_out', sa.String(20), default='cut'))
    op.add_column('segments', sa.Column('transition_in_duration', sa.Float, default=0.0))
    op.add_column('segments', sa.Column('transition_out_duration', sa.Float, default=0.0))
    
    # Add Phase 3 fields to segments table
    op.add_column('segments', sa.Column('text_overlay', sa.Text))
    op.add_column('segments', sa.Column('text_overlay_position', sa.String(20), default='bottom'))
    op.add_column('segments', sa.Column('text_overlay_start', sa.Float))
    op.add_column('segments', sa.Column('text_overlay_duration', sa.Float))
    
    # Add Phase 3 fields to users table
    op.add_column('users', sa.Column('youtube_credentials', sa.JSON))


def downgrade():
    """Remove Phase 2 and Phase 3 fields."""
    
    # Remove Phase 3 fields from users table
    op.drop_column('users', 'youtube_credentials')
    
    # Remove Phase 3 fields from segments table
    op.drop_column('segments', 'text_overlay_duration')
    op.drop_column('segments', 'text_overlay_start')
    op.drop_column('segments', 'text_overlay_position')
    op.drop_column('segments', 'text_overlay')
    
    # Remove Phase 2 fields from segments table
    op.drop_column('segments', 'transition_out_duration')
    op.drop_column('segments', 'transition_in_duration')
    op.drop_column('segments', 'transition_out')
    op.drop_column('segments', 'transition_in')
    op.drop_column('segments', 'custom_description')
    op.drop_column('segments', 'custom_title')
    
    # Remove Phase 3 fields from projects table
    op.drop_column('projects', 'youtube_url')
    op.drop_column('projects', 'ai_tags')
    op.drop_column('projects', 'ai_description')
    op.drop_column('projects', 'ai_title')
    op.drop_column('projects', 'youtube_upload_enabled')
    op.drop_column('projects', 'auto_chapters')
    
    # Remove Phase 2 fields from projects table
    op.drop_column('projects', 'background_music_volume')
    op.drop_column('projects', 'background_music_url')
    op.drop_column('projects', 'outro_file_url')
    op.drop_column('projects', 'intro_file_url')
    op.drop_column('projects', 'transition_duration')
    op.drop_column('projects', 'transition_type')
    op.drop_column('projects', 'compilation_mode')
