#!/usr/bin/env python3
"""
UniDynamics Flask Application Runner
"""

import os
from flask.cli import FlaskGroup
from app import create_app, db, init_celery
from app.models import User, Project, Video, Segment, Transcript

# Create Flask app
app = create_app()

# Initialize Celery
celery = init_celery(app)

# Create CLI group
cli = FlaskGroup(app)


@cli.command("init-db")
def init_db():
    """Initialize the database."""
    with app.app_context():
        db.create_all()
        print("Database initialized successfully!")


@cli.command("reset-db")
def reset_db():
    """Reset the database (WARNING: This will delete all data!)."""
    with app.app_context():
        db.drop_all()
        db.create_all()
        print("Database reset successfully!")


@cli.command("create-admin")
def create_admin():
    """Create an admin user."""
    with app.app_context():
        email = input("Enter admin email: ")
        username = input("Enter admin username: ")
        password = input("Enter admin password: ")
        
        # Check if user already exists
        if User.query.filter_by(email=email).first():
            print("User with this email already exists!")
            return
        
        if User.query.filter_by(username=username).first():
            print("User with this username already exists!")
            return
        
        # Create admin user
        admin = User(
            email=email,
            username=username,
            password=password,
            first_name="Admin",
            last_name="User",
            is_verified=True
        )
        
        db.session.add(admin)
        db.session.commit()
        
        print(f"Admin user '{username}' created successfully!")


@cli.command("test-db")
def test_db():
    """Test database connection."""
    with app.app_context():
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            print("Database connection successful!")
            
            # Show table info
            tables = db.engine.table_names()
            print(f"Tables in database: {tables}")
            
        except Exception as e:
            print(f"Database connection failed: {e}")


if __name__ == '__main__':
    cli()
