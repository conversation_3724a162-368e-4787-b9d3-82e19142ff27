import os
import json
from typing import Dict, List, Any, Optional
from flask import current_app
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from googleapiclient.errors import HttpError

from app.models.project import Project
from app.models.user import User


class YouTubeUploadService:
    """Service for uploading videos to YouTube with metadata."""
    
    def __init__(self):
        self.client_secrets_file = current_app.config.get('YOUTUBE_CLIENT_SECRETS_FILE')
        self.scopes = ['https://www.googleapis.com/auth/youtube.upload']
        self.api_service_name = 'youtube'
        self.api_version = 'v3'
    
    def get_auth_url(self, user_id: int) -> Optional[str]:
        """Get YouTube OAuth authorization URL."""
        try:
            if not self.client_secrets_file or not os.path.exists(self.client_secrets_file):
                current_app.logger.error("YouTube client secrets file not found")
                return None
            
            flow = Flow.from_client_secrets_file(
                self.client_secrets_file,
                scopes=self.scopes,
                redirect_uri=current_app.config.get('YOUTUBE_REDIRECT_URI')
            )
            
            flow.state = str(user_id)  # Store user ID in state
            
            auth_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )
            
            return auth_url
            
        except Exception as e:
            current_app.logger.error(f"Error getting YouTube auth URL: {str(e)}")
            return None
    
    def handle_oauth_callback(self, user_id: int, authorization_code: str) -> bool:
        """Handle OAuth callback and store credentials."""
        try:
            flow = Flow.from_client_secrets_file(
                self.client_secrets_file,
                scopes=self.scopes,
                redirect_uri=current_app.config.get('YOUTUBE_REDIRECT_URI')
            )
            
            flow.fetch_token(code=authorization_code)
            credentials = flow.credentials
            
            # Store credentials for user
            user = User.query.get(user_id)
            if user:
                user.youtube_credentials = {
                    'token': credentials.token,
                    'refresh_token': credentials.refresh_token,
                    'token_uri': credentials.token_uri,
                    'client_id': credentials.client_id,
                    'client_secret': credentials.client_secret,
                    'scopes': credentials.scopes
                }
                
                from app import db
                db.session.commit()
                return True
            
            return False
            
        except Exception as e:
            current_app.logger.error(f"Error handling YouTube OAuth callback: {str(e)}")
            return False
    
    def upload_video(self, project_id: int, video_file_path: str, 
                    metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Upload video to YouTube with metadata."""
        try:
            project = Project.query.get(project_id)
            if not project:
                return None
            
            user = User.query.get(project.user_id)
            if not user or not user.youtube_credentials:
                return None
            
            # Build YouTube service
            youtube = self._build_youtube_service(user.youtube_credentials)
            if not youtube:
                return None
            
            # Prepare video metadata
            video_metadata = self._prepare_video_metadata(project, metadata)
            
            # Upload video
            video_id = self._upload_video_file(
                youtube, 
                video_file_path, 
                video_metadata
            )
            
            if video_id:
                # Add chapters if enabled
                if project.auto_chapters:
                    self._add_video_chapters(youtube, video_id, project)
                
                return f"https://www.youtube.com/watch?v={video_id}"
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"Error uploading video to YouTube: {str(e)}")
            return None
    
    def _build_youtube_service(self, credentials_data: Dict):
        """Build YouTube API service from stored credentials."""
        try:
            credentials = Credentials(
                token=credentials_data['token'],
                refresh_token=credentials_data.get('refresh_token'),
                token_uri=credentials_data['token_uri'],
                client_id=credentials_data['client_id'],
                client_secret=credentials_data['client_secret'],
                scopes=credentials_data['scopes']
            )
            
            return build(
                self.api_service_name, 
                self.api_version, 
                credentials=credentials
            )
            
        except Exception as e:
            current_app.logger.error(f"Error building YouTube service: {str(e)}")
            return None
    
    def _prepare_video_metadata(self, project: Project, 
                              custom_metadata: Optional[Dict] = None) -> Dict:
        """Prepare video metadata for YouTube upload."""
        metadata = {
            'snippet': {
                'title': project.ai_title or project.title or 'Video Summary',
                'description': project.ai_description or project.description or '',
                'tags': project.ai_tags or [],
                'categoryId': '22',  # People & Blogs
                'defaultLanguage': 'en',
                'defaultAudioLanguage': 'en'
            },
            'status': {
                'privacyStatus': 'unlisted',  # Start as unlisted for safety
                'selfDeclaredMadeForKids': False
            }
        }
        
        # Apply custom metadata if provided
        if custom_metadata:
            if 'title' in custom_metadata:
                metadata['snippet']['title'] = custom_metadata['title']
            if 'description' in custom_metadata:
                metadata['snippet']['description'] = custom_metadata['description']
            if 'tags' in custom_metadata:
                metadata['snippet']['tags'] = custom_metadata['tags']
            if 'privacy' in custom_metadata:
                metadata['status']['privacyStatus'] = custom_metadata['privacy']
        
        return metadata
    
    def _upload_video_file(self, youtube, video_file_path: str, 
                          metadata: Dict) -> Optional[str]:
        """Upload the actual video file to YouTube."""
        try:
            media = MediaFileUpload(
                video_file_path,
                chunksize=-1,
                resumable=True,
                mimetype='video/mp4'
            )
            
            request = youtube.videos().insert(
                part=','.join(metadata.keys()),
                body=metadata,
                media_body=media
            )
            
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    current_app.logger.info(f"Upload progress: {int(status.progress() * 100)}%")
            
            if 'id' in response:
                return response['id']
            else:
                current_app.logger.error(f"YouTube upload failed: {response}")
                return None
                
        except HttpError as e:
            current_app.logger.error(f"YouTube API error during upload: {str(e)}")
            return None
        except Exception as e:
            current_app.logger.error(f"Error uploading video file: {str(e)}")
            return None
    
    def _add_video_chapters(self, youtube, video_id: str, project: Project):
        """Add chapter markers to the uploaded video."""
        try:
            from app.services.advanced_ai_service import AdvancedAIService
            ai_service = AdvancedAIService()
            
            chapters = ai_service.generate_chapters(project.id)
            if not chapters:
                return
            
            # Format chapters for YouTube description
            chapter_text = "\n\nChapters:\n"
            for chapter in chapters:
                timestamp = self._format_timestamp(chapter['start_time'])
                chapter_text += f"{timestamp} {chapter['title']}\n"
            
            # Get current video details
            video_response = youtube.videos().list(
                part='snippet',
                id=video_id
            ).execute()
            
            if video_response['items']:
                current_description = video_response['items'][0]['snippet']['description']
                updated_description = current_description + chapter_text
                
                # Update video description with chapters
                youtube.videos().update(
                    part='snippet',
                    body={
                        'id': video_id,
                        'snippet': {
                            **video_response['items'][0]['snippet'],
                            'description': updated_description
                        }
                    }
                ).execute()
                
                current_app.logger.info(f"Added {len(chapters)} chapters to video {video_id}")
            
        except Exception as e:
            current_app.logger.error(f"Error adding video chapters: {str(e)}")
    
    def _format_timestamp(self, seconds: float) -> str:
        """Format seconds to MM:SS or HH:MM:SS timestamp."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes}:{secs:02d}"
    
    def get_upload_status(self, video_id: str, user_id: int) -> Optional[Dict]:
        """Get the upload status of a YouTube video."""
        try:
            user = User.query.get(user_id)
            if not user or not user.youtube_credentials:
                return None
            
            youtube = self._build_youtube_service(user.youtube_credentials)
            if not youtube:
                return None
            
            response = youtube.videos().list(
                part='status,processingDetails',
                id=video_id
            ).execute()
            
            if response['items']:
                video_data = response['items'][0]
                return {
                    'upload_status': video_data['status']['uploadStatus'],
                    'privacy_status': video_data['status']['privacyStatus'],
                    'processing_status': video_data.get('processingDetails', {}).get('processingStatus'),
                    'processing_progress': video_data.get('processingDetails', {}).get('processingProgress')
                }
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"Error getting upload status: {str(e)}")
            return None
    
    def update_video_privacy(self, video_id: str, user_id: int, 
                           privacy_status: str) -> bool:
        """Update the privacy status of a YouTube video."""
        try:
            user = User.query.get(user_id)
            if not user or not user.youtube_credentials:
                return False
            
            youtube = self._build_youtube_service(user.youtube_credentials)
            if not youtube:
                return False
            
            youtube.videos().update(
                part='status',
                body={
                    'id': video_id,
                    'status': {
                        'privacyStatus': privacy_status
                    }
                }
            ).execute()
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Error updating video privacy: {str(e)}")
            return False
