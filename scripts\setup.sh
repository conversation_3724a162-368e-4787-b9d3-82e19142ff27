#!/bin/bash

# UniDynamics Setup Script
# This script sets up the development environment for UniDynamics

set -e

echo "🚀 Setting up UniDynamics Development Environment"
echo "=================================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed (for local development)
if ! command -v node &> /dev/null; then
    echo "⚠️  Node.js is not installed. You'll need it for local frontend development."
    echo "   You can still use Docker for everything."
fi

# Check if Python is installed (for local development)
if ! command -v python3 &> /dev/null; then
    echo "⚠️  Python 3 is not installed. You'll need it for local backend development."
    echo "   You can still use Docker for everything."
fi

echo ""
echo "📁 Setting up project structure..."

# Create necessary directories
mkdir -p backend/uploads
mkdir -p backend/logs
mkdir -p frontend/build
mkdir -p docs/api
mkdir -p tests/backend
mkdir -p tests/frontend

echo "✅ Project directories created"

echo ""
echo "🔧 Setting up environment files..."

# Backend environment file
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "✅ Backend .env file created from example"
    echo "   Please edit backend/.env with your configuration"
else
    echo "ℹ️  Backend .env file already exists"
fi

# Frontend environment file
if [ ! -f frontend/.env ]; then
    cp frontend/.env.example frontend/.env
    echo "✅ Frontend .env file created from example"
    echo "   Please edit frontend/.env with your configuration"
else
    echo "ℹ️  Frontend .env file already exists"
fi

echo ""
echo "🐳 Setting up Docker environment..."

# Build and start services
cd docker
docker-compose up -d postgres redis

echo "⏳ Waiting for database to be ready..."
sleep 10

# Initialize database
docker-compose run --rm backend python run.py init-db

echo "✅ Database initialized"

echo ""
echo "🤖 Setting up Ollama (AI Models)..."

# Start Ollama
docker-compose up -d ollama

echo "⏳ Waiting for Ollama to be ready..."
sleep 15

# Pull required models
echo "📥 Downloading AI models (this may take a while)..."
docker-compose exec ollama ollama pull llama2
docker-compose exec ollama ollama pull mistral

echo "✅ AI models downloaded"

echo ""
echo "🎯 Setup Options:"
echo ""
echo "1. 🐳 Full Docker Development (Recommended for beginners)"
echo "   - Everything runs in Docker containers"
echo "   - Run: cd docker && docker-compose up"
echo "   - Access: http://localhost:3000"
echo ""
echo "2. 🔧 Hybrid Development (Recommended for developers)"
echo "   - Database and services in Docker"
echo "   - Frontend and backend run locally"
echo "   - Better for debugging and development"
echo ""
echo "3. 💻 Local Development"
echo "   - Everything runs locally"
echo "   - Requires all dependencies installed"
echo ""

read -p "Choose setup option (1/2/3): " choice

case $choice in
    1)
        echo "🐳 Starting full Docker environment..."
        docker-compose up -d
        echo ""
        echo "✅ UniDynamics is running!"
        echo "   Frontend: http://localhost:3000"
        echo "   Backend API: http://localhost:5000"
        echo "   Stop with: docker-compose down"
        ;;
    2)
        echo "🔧 Setting up hybrid development..."
        
        # Keep only infrastructure services running
        docker-compose up -d postgres redis ollama
        
        echo ""
        echo "📦 Installing frontend dependencies..."
        cd ../frontend
        npm install
        
        echo ""
        echo "🐍 Setting up backend virtual environment..."
        cd ../backend
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        
        echo ""
        echo "✅ Hybrid setup complete!"
        echo ""
        echo "To start development:"
        echo "1. Backend: cd backend && source venv/bin/activate && python run.py"
        echo "2. Frontend: cd frontend && npm start"
        echo "3. Celery Worker: cd backend && source venv/bin/activate && celery -A app.celery worker --loglevel=info"
        ;;
    3)
        echo "💻 Local development setup..."
        echo "Please ensure you have:"
        echo "- PostgreSQL running on localhost:5432"
        echo "- Redis running on localhost:6379"
        echo "- Ollama running on localhost:11434"
        echo ""
        echo "Then follow the hybrid setup instructions."
        ;;
    *)
        echo "Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📚 Next steps:"
echo "1. Read the README.md for detailed instructions"
echo "2. Check the documentation in docs/"
echo "3. Create your first project at http://localhost:3000"
echo ""
echo "🆘 Need help?"
echo "- Check logs: docker-compose logs [service-name]"
echo "- Restart services: docker-compose restart"
echo "- Reset database: docker-compose run --rm backend python run.py reset-db"
echo ""
echo "Happy coding! 🚀"
