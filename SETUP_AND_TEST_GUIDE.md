# UniDynamics Setup and Testing Guide

## 🔧 Step 1: Prerequisites Installation

### Check Current Installations
Run these commands to check what you already have:

```powershell
# Check Python
python --version
# Should be Python 3.9+

# Check Node.js
node --version
# Should be 18+

# Check PostgreSQL
psql --version
# Should be 13+

# Check Redis
redis-cli --version
# Should be 6+

# Check FFmpeg
ffmpeg -version
# Should be 4.4+
```

### Install Missing Prerequisites

**If Python is missing or outdated:**
```powershell
# Download and install Python 3.11 from python.org
# Make sure to check "Add Python to PATH"
```

**If Node.js is missing:**
```powershell
# Download and install Node.js 18+ from nodejs.org
```

**If PostgreSQL is missing:**
```powershell
# Download and install PostgreSQL from postgresql.org
# Remember the password you set for the postgres user
```

**If Redis is missing:**
```powershell
# Download Redis for Windows from:
# https://github.com/microsoftarchive/redis/releases
# Or use WSL/Docker
```

**If FFmpeg is missing:**
```powershell
# Download FFmpeg from ffmpeg.org
# Extract to C:\ffmpeg
# Add C:\ffmpeg\bin to your PATH environment variable
```

**Install Ollama:**
```powershell
# Download and install Ollama from ollama.ai
# After installation, run:
ollama pull llama2
```

## 🗄️ Step 2: Database Setup

### Create Database
```powershell
# Open Command Prompt as Administrator
# Connect to PostgreSQL
psql -U postgres

# In PostgreSQL prompt:
CREATE DATABASE unidynamics_dev;
CREATE USER unidynamics WITH PASSWORD 'dev_password_123';
GRANT ALL PRIVILEGES ON DATABASE unidynamics_dev TO unidynamics;
\q
```

### Start Redis
```powershell
# Start Redis server
redis-server
# Keep this terminal open
```

## 🔧 Step 3: Backend Setup

### Navigate to Backend Directory
```powershell
cd backend
```

### Create Virtual Environment
```powershell
python -m venv venv
venv\Scripts\activate
```

### Install Dependencies
```powershell
pip install -r requirements.txt
```

### Environment Configuration
Create `.env` file in backend directory:
```env
# Database
DATABASE_URL=postgresql://unidynamics:dev_password_123@localhost:5432/unidynamics_dev

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=your-super-secret-jwt-key-for-development

# Flask
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=True

# AI Services
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# File Storage
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=2147483648

# YouTube API (optional for testing)
YOUTUBE_CLIENT_SECRETS_FILE=config/youtube_client_secrets.json
YOUTUBE_REDIRECT_URI=http://localhost:3000/auth/youtube/callback
```

### Initialize Database
```powershell
# Create uploads directory
mkdir uploads

# Initialize database
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### Start Backend Services

**Terminal 1 - Flask API:**
```powershell
cd backend
venv\Scripts\activate
python app.py
```

**Terminal 2 - Celery Worker:**
```powershell
cd backend
venv\Scripts\activate
celery -A app.celery worker --loglevel=info --pool=solo
```

**Terminal 3 - Celery Beat (optional):**
```powershell
cd backend
venv\Scripts\activate
celery -A app.celery beat --loglevel=info
```

## 🎨 Step 4: Frontend Setup

### Navigate to Frontend Directory
```powershell
cd frontend
```

### Install Dependencies
```powershell
npm install
```

### Environment Configuration
Create `.env` file in frontend directory:
```env
REACT_APP_API_BASE_URL=http://localhost:5000/api
REACT_APP_ENVIRONMENT=development
REACT_APP_DEBUG=true
```

### Start Frontend
```powershell
npm start
```

The application should open at `http://localhost:3000`

## ✅ Step 5: Verify Installation

### Check All Services
1. **Frontend**: http://localhost:3000 (should show login page)
2. **Backend API**: http://localhost:5000/health (should return {"status": "healthy"})
3. **Database**: Should be accessible via psql
4. **Redis**: Should be running on port 6379
5. **Ollama**: Should be running on port 11434

### Service Status Check
```powershell
# Check if all ports are listening
netstat -an | findstr "3000 5000 5432 6379 11434"
```

## 🧪 Step 6: Complete Feature Testing

### Test 1: User Registration and Login

1. **Open browser to http://localhost:3000**
2. **Click "Register"**
3. **Create account:**
   - Email: <EMAIL>
   - Username: testuser
   - Password: TestPassword123!
4. **Login with credentials**
5. **Verify dashboard loads**

### Test 2: Project Creation

1. **Click "Create New Project"**
2. **Fill project details:**
   - Title: "Test Video Summary"
   - Description: "Testing all UniDynamics features"
3. **Click "Create Project"**
4. **Verify project appears in dashboard**

### Test 3: Video Processing (Phase 1)

1. **Click on your project**
2. **Click "Add Video"**
3. **Enter YouTube URL:** `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
4. **Click "Add Video"**
5. **Monitor processing status:**
   - Should show "Downloading"
   - Then "Transcribing"
   - Then "Analyzing"
   - Finally "Completed"
6. **Verify video player loads**
7. **Test transcript viewer:**
   - Search for words in transcript
   - Click on transcript lines to seek video
8. **Test segment timeline:**
   - Click on segments to jump to time
   - Verify segment overlays on video
9. **Test segment selector:**
   - Select/deselect segments
   - Use "Select All" and "Select None"
   - Filter by importance

### Test 4: Multi-Video Features (Phase 2)

1. **Add second video to project**
2. **Go to project settings:**
   - Change compilation mode to "Multi-Video"
   - Set transition type to "Fade"
   - Set transition duration to 1.0 seconds
3. **Test multi-video timeline:**
   - Drag segments to reorder
   - Right-click segments for context menu
   - Edit transition settings
4. **Upload intro video (optional):**
   - Click "Upload Intro"
   - Select a short MP4 file
5. **Test background music (optional):**
   - Click "Upload Music"
   - Select an MP3 file
   - Adjust volume slider

### Test 5: AI Features (Phase 3)

1. **Generate AI metadata:**
   - Click "Generate AI Metadata"
   - Wait for completion
   - Verify title, description, and tags are generated
2. **Edit AI metadata:**
   - Click edit buttons
   - Modify generated content
   - Save changes
3. **View chapters:**
   - Check auto-generated chapters
   - Verify timestamps and titles
4. **Content analysis:**
   - View sentiment analysis
   - Check content recommendations

### Test 6: Video Rendering

1. **Select desired segments**
2. **Click "Render Video"**
3. **Monitor rendering progress**
4. **Wait for completion**
5. **Test download:**
   - Click "Download Video"
   - Verify file downloads
6. **Test sharing:**
   - Click "Share"
   - Copy different share formats

### Test 7: YouTube Upload (Phase 3) - Optional

**Note: Requires YouTube API credentials**

1. **Set up YouTube API:**
   - Go to Google Cloud Console
   - Create project and enable YouTube Data API
   - Create OAuth 2.0 credentials
   - Download client secrets JSON
   - Place in `backend/config/youtube_client_secrets.json`

2. **Test YouTube upload:**
   - Click "Upload to YouTube"
   - Authorize YouTube account
   - Configure upload settings
   - Start upload
   - Monitor progress
   - Verify video appears on YouTube

## 🐛 Troubleshooting Common Issues

### Backend Issues

**Database Connection Error:**
```powershell
# Check PostgreSQL is running
pg_ctl status

# Restart if needed
pg_ctl restart
```

**Redis Connection Error:**
```powershell
# Check Redis is running
redis-cli ping
# Should return "PONG"
```

**Ollama Not Working:**
```powershell
# Check Ollama status
ollama list

# Pull model if missing
ollama pull llama2
```

**FFmpeg Not Found:**
```powershell
# Check FFmpeg in PATH
ffmpeg -version

# Add to PATH if needed
```

### Frontend Issues

**API Connection Error:**
- Verify backend is running on port 5000
- Check CORS settings in backend
- Verify API base URL in frontend .env

**Video Player Not Loading:**
- Check video file permissions
- Verify FFmpeg processed video correctly
- Check browser console for errors

### Processing Issues

**Video Download Fails:**
- Check internet connection
- Verify YouTube URL is valid and public
- Check yt-dlp is installed correctly

**Transcription Fails:**
- Verify Whisper model is downloaded
- Check audio extraction worked
- Review Celery worker logs

**AI Analysis Fails:**
- Check Ollama is running and accessible
- Verify llama2 model is pulled
- Check Ollama logs for errors

## 📊 Performance Testing

### Load Testing
```powershell
# Test multiple concurrent video processing
# Create 3-5 projects simultaneously
# Add videos to each project
# Monitor system resources
```

### Memory Usage
```powershell
# Monitor memory usage during video processing
# Task Manager -> Performance -> Memory
# Should not exceed available RAM
```

### Processing Speed
```powershell
# Time video processing stages:
# Download: Should be < 2x video length
# Transcription: Should be < 1x video length  
# AI Analysis: Should be < 30 seconds
# Rendering: Should be < 3x final video length
```

## ✅ Success Criteria

Your UniDynamics installation is successful if:

- [ ] All services start without errors
- [ ] User registration and login work
- [ ] Video download and processing complete
- [ ] Transcript generation works
- [ ] AI segment analysis completes
- [ ] Video player shows segments correctly
- [ ] Multi-video timeline functions
- [ ] AI metadata generation works
- [ ] Video rendering produces output file
- [ ] Download functionality works

## 🎉 Next Steps

Once testing is complete:

1. **Customize configuration** for your needs
2. **Set up production environment** using DEPLOYMENT_GUIDE.md
3. **Configure YouTube API** for upload features
4. **Set up monitoring** and logging
5. **Create user documentation** for your team

## 🆘 Getting Help

If you encounter issues:

1. **Check logs:**
   - Backend: Flask console output
   - Celery: Worker console output
   - Frontend: Browser developer console

2. **Common log locations:**
   - Flask: Terminal where you ran `python app.py`
   - Celery: Terminal where you ran celery worker
   - PostgreSQL: Check PostgreSQL logs
   - Redis: Check Redis logs

3. **Debug mode:**
   - Set `FLASK_DEBUG=True` in backend .env
   - Set `REACT_APP_DEBUG=true` in frontend .env

The complete UniDynamics platform should now be running with all Phase 1, 2, and 3 features functional!
