import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

const ProjectsPage = () => {
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Projects
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="large"
        >
          New Project
        </Button>
      </Box>
      
      <Typography variant="body1" color="text.secondary">
        Projects page - Implementation in progress...
      </Typography>
    </Box>
  );
};

export default ProjectsPage;
