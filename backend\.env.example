# Flask Configuration
FLASK_APP=app
FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-key-change-in-production

# Database
DATABASE_URL=postgresql://unidynamics:unidynamics_password@localhost:5432/unidynamics

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# YouTube API (optional - for enhanced metadata)
YOUTUBE_API_KEY=

# OpenAI (optional, for Whisper API instead of local)
OPENAI_API_KEY=

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2  # or mistral, codellama, etc.

# Cloud Storage (optional - choose one)
# AWS S3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
S3_BUCKET=

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT=
GCS_BUCKET=
GOOGLE_APPLICATION_CREDENTIALS=

# File Upload Limits
MAX_CONTENT_LENGTH=1073741824  # 1GB in bytes
UPLOAD_FOLDER=uploads

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/1

# Monitoring (optional)
SENTRY_DSN=

# Development
DEBUG=True
