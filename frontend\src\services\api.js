import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {}, {
            headers: { Authorization: `Bearer ${refreshToken}` }
          });
          
          const newToken = response.data.access_token;
          localStorage.setItem('access_token', newToken);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  setAuthToken: (token) => {
    if (token) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      localStorage.setItem('access_token', token);
    } else {
      delete apiClient.defaults.headers.common['Authorization'];
      localStorage.removeItem('access_token');
    }
  },

  login: (email, password) => 
    apiClient.post('/auth/login', { email, password }),

  register: (userData) => 
    apiClient.post('/auth/register', userData),

  logout: () => 
    apiClient.post('/auth/logout'),

  getProfile: () => 
    apiClient.get('/auth/profile'),

  updateProfile: (profileData) => 
    apiClient.put('/auth/profile', profileData),

  changePassword: (currentPassword, newPassword) => 
    apiClient.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword
    }),

  refreshToken: (refreshToken) => 
    apiClient.post('/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${refreshToken}` }
    }),
};

// Projects API
export const projectsAPI = {
  getProjects: (params = {}) => 
    apiClient.get('/projects', { params }),

  createProject: (projectData) => 
    apiClient.post('/projects', projectData),

  getProject: (projectId) => 
    apiClient.get(`/projects/${projectId}`),

  updateProject: (projectId, projectData) => 
    apiClient.put(`/projects/${projectId}`, projectData),

  deleteProject: (projectId) => 
    apiClient.delete(`/projects/${projectId}`),

  getProjectVideos: (projectId) => 
    apiClient.get(`/projects/${projectId}/videos`),

  getProjectSegments: (projectId, params = {}) => 
    apiClient.get(`/projects/${projectId}/segments`, { params }),

  selectSegments: (projectId, segments) => 
    apiClient.post(`/projects/${projectId}/segments/select`, { segments }),

  getProjectStatus: (projectId) => 
    apiClient.get(`/projects/${projectId}/status`),
};

// Videos API
export const videosAPI = {
  addVideo: (projectId, youtubeUrl) => 
    apiClient.post('/videos', { project_id: projectId, youtube_url: youtubeUrl }),

  getVideo: (videoId) => 
    apiClient.get(`/videos/${videoId}`),

  deleteVideo: (videoId) => 
    apiClient.delete(`/videos/${videoId}`),

  getVideoTranscript: (videoId) => 
    apiClient.get(`/videos/${videoId}/transcript`),

  getVideoSegments: (videoId, params = {}) => 
    apiClient.get(`/videos/${videoId}/segments`, { params }),

  getVideoStatus: (videoId) => 
    apiClient.get(`/videos/${videoId}/status`),

  reprocessVideo: (videoId) => 
    apiClient.post(`/videos/${videoId}/reprocess`),

  validateUrl: (youtubeUrl) => 
    apiClient.post('/videos/validate-url', { youtube_url: youtubeUrl }),
};

// Processing API
export const processingAPI = {
  startRender: (projectId) => 
    apiClient.post('/processing/render', { project_id: projectId }),

  getTaskStatus: (taskId) => 
    apiClient.get(`/processing/status/${taskId}`),

  cancelTask: (taskId) => 
    apiClient.post(`/processing/cancel/${taskId}`),

  getDownloadUrl: (projectId) => 
    apiClient.get(`/processing/project/${projectId}/download`),

  regenerateSegments: (projectId) => 
    apiClient.post(`/processing/project/${projectId}/regenerate-segments`),

  generateMetadata: (projectId) => 
    apiClient.post(`/processing/project/${projectId}/generate-metadata`),

  healthCheck: () => 
    apiClient.get('/processing/health'),
};

// Utility functions
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    return error.response.data?.error || 'An error occurred';
  } else if (error.request) {
    // Request was made but no response received
    return 'Network error - please check your connection';
  } else {
    // Something else happened
    return error.message || 'An unexpected error occurred';
  }
};

export const isNetworkError = (error) => {
  return !error.response && error.request;
};

export default apiClient;
