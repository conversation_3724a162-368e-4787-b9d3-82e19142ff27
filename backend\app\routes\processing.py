from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from app import db
from app.models.project import Project, ProjectStatus
from app.models.segment import Segment
from app.tasks.video_tasks import render_final_video

processing_bp = Blueprint('processing', __name__)


@processing_bp.route('/render', methods=['POST'])
@jwt_required()
def start_render():
    """Start rendering the final video for a project."""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data.get('project_id'):
            return jsonify({'error': 'Project ID is required'}), 400
        
        project_id = data['project_id']
        
        # Check if project exists and belongs to user
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Check if project has selected segments
        selected_segments = Segment.query.filter_by(
            project_id=project_id, is_selected=True
        ).count()
        
        if selected_segments == 0:
            return jsonify({'error': 'No segments selected for rendering'}), 400
        
        # Check if project is in a valid state for rendering
        if project.status == ProjectStatus.PROCESSING:
            return jsonify({'error': 'Project is already being processed'}), 400
        
        # Update project status
        project.update_status(ProjectStatus.PROCESSING, 0.5)
        
        # Start rendering task
        task = render_final_video.delay(project_id)
        
        return jsonify({
            'message': 'Rendering started',
            'task_id': task.id,
            'project_status': project.status.value
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Start render error: {str(e)}")
        return jsonify({'error': 'Failed to start rendering'}), 500


@processing_bp.route('/status/<task_id>', methods=['GET'])
@jwt_required()
def get_task_status(task_id):
    """Get status of a processing task."""
    try:
        from app import celery
        
        task = celery.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'state': task.state,
                'status': 'Task is waiting to be processed'
            }
        elif task.state == 'PROGRESS':
            response = {
                'state': task.state,
                'current': task.info.get('current', 0),
                'total': task.info.get('total', 1),
                'status': task.info.get('status', '')
            }
        elif task.state == 'SUCCESS':
            response = {
                'state': task.state,
                'result': task.info
            }
        else:  # FAILURE
            response = {
                'state': task.state,
                'error': str(task.info)
            }
        
        return jsonify(response), 200
        
    except Exception as e:
        current_app.logger.error(f"Get task status error: {str(e)}")
        return jsonify({'error': 'Failed to get task status'}), 500


@processing_bp.route('/cancel/<task_id>', methods=['POST'])
@jwt_required()
def cancel_task(task_id):
    """Cancel a processing task."""
    try:
        from app import celery
        
        celery.control.revoke(task_id, terminate=True)
        
        return jsonify({'message': 'Task cancelled'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Cancel task error: {str(e)}")
        return jsonify({'error': 'Failed to cancel task'}), 500


@processing_bp.route('/project/<int:project_id>/download', methods=['GET'])
@jwt_required()
def get_download_url(project_id):
    """Get download URL for completed project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if project.status != ProjectStatus.COMPLETED:
            return jsonify({'error': 'Project is not completed'}), 400
        
        if not project.output_url:
            return jsonify({'error': 'Output file not available'}), 404
        
        return jsonify({
            'download_url': project.output_url,
            'filename': project.output_filename,
            'duration': project.output_duration,
            'attribution': project.attribution_text
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get download URL error: {str(e)}")
        return jsonify({'error': 'Failed to get download URL'}), 500


@processing_bp.route('/project/<int:project_id>/regenerate-segments', methods=['POST'])
@jwt_required()
def regenerate_segments(project_id):
    """Regenerate AI-suggested segments for all videos in project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Check if project has videos with transcripts
        videos_with_transcripts = []
        for video in project.videos:
            if video.transcript:
                videos_with_transcripts.append(video.id)
        
        if not videos_with_transcripts:
            return jsonify({'error': 'No videos with transcripts found'}), 400
        
        # Start segment generation tasks
        from app.tasks.ai_tasks import generate_segments_for_video
        
        task_ids = []
        for video_id in videos_with_transcripts:
            task = generate_segments_for_video.delay(video_id)
            task_ids.append(task.id)
        
        return jsonify({
            'message': 'Segment regeneration started',
            'task_ids': task_ids,
            'videos_count': len(videos_with_transcripts)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Regenerate segments error: {str(e)}")
        return jsonify({'error': 'Failed to regenerate segments'}), 500


@processing_bp.route('/project/<int:project_id>/generate-metadata', methods=['POST'])
@jwt_required()
def generate_metadata(project_id):
    """Generate AI-suggested metadata for project."""
    try:
        current_user_id = get_jwt_identity()
        
        project = Project.query.filter_by(
            id=project_id, user_id=current_user_id
        ).first()
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        # Check if project has selected segments
        selected_segments = Segment.query.filter_by(
            project_id=project_id, is_selected=True
        ).count()
        
        if selected_segments == 0:
            return jsonify({'error': 'No segments selected'}), 400
        
        # Start metadata generation task
        from app.tasks.ai_tasks import generate_project_metadata
        
        task = generate_project_metadata.delay(project_id)
        
        return jsonify({
            'message': 'Metadata generation started',
            'task_id': task.id
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Generate metadata error: {str(e)}")
        return jsonify({'error': 'Failed to generate metadata'}), 500


@processing_bp.route('/health', methods=['GET'])
def health_check():
    """Health check for processing service."""
    try:
        from app import celery
        
        # Check Celery connection
        inspect = celery.control.inspect()
        stats = inspect.stats()
        
        if stats:
            active_workers = len(stats)
            return jsonify({
                'status': 'healthy',
                'active_workers': active_workers,
                'celery_connected': True
            }), 200
        else:
            return jsonify({
                'status': 'degraded',
                'active_workers': 0,
                'celery_connected': False
            }), 200
            
    except Exception as e:
        current_app.logger.error(f"Processing health check error: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500
