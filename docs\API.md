# UniDynamics API Documentation

This document describes the REST API endpoints for UniDynamics.

## Base URL
```
Development: http://localhost:5000/api
Production: https://api.unidynamics.com/api
```

## Authentication

UniDynamics uses JWT (JSON Web Tokens) for authentication.

### Headers
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "full_name": "<PERSON>",
    "is_active": true,
    "is_verified": false,
    "created_at": "2024-01-01T00:00:00"
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### POST /auth/login
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": { /* user object */ },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Headers:**
```
Authorization: Bearer <refresh_token>
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": { /* user object */ }
}
```

### GET /auth/profile
Get current user profile.

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "avatar_url": null,
    "is_active": true,
    "is_verified": false,
    "created_at": "2024-01-01T00:00:00",
    "last_login": "2024-01-01T12:00:00"
  }
}
```

## Project Endpoints

### GET /projects
Get all projects for the authenticated user.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 10, max: 50)
- `status` (string): Filter by status (draft, processing, completed, failed)
- `search` (string): Search in title and description

**Response:**
```json
{
  "projects": [
    {
      "id": 1,
      "title": "My First Project",
      "description": "A test project",
      "status": "draft",
      "progress": 0.0,
      "output_url": null,
      "output_duration": null,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00",
      "total_duration": 0.0,
      "source_videos_count": 0
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 10,
    "total": 1,
    "pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

### POST /projects
Create a new project.

**Request Body:**
```json
{
  "title": "My New Project",
  "description": "Project description",
  "max_duration": 600,
  "include_intro": false,
  "include_outro": false
}
```

**Response:**
```json
{
  "message": "Project created successfully",
  "project": { /* project object */ }
}
```

### GET /projects/{project_id}
Get a specific project with full details.

**Response:**
```json
{
  "project": {
    "id": 1,
    "title": "My Project",
    "description": "Description",
    "status": "draft",
    "progress": 0.3,
    "videos": [
      {
        "id": 1,
        "youtube_id": "dQw4w9WgXcQ",
        "title": "Video Title",
        "status": "ready",
        "duration": 212.5,
        "segments": [ /* segment objects */ ]
      }
    ],
    "segments": [ /* all segments */ ]
  }
}
```

## Video Endpoints

### POST /videos
Add a YouTube video to a project.

**Request Body:**
```json
{
  "project_id": 1,
  "youtube_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
}
```

**Response:**
```json
{
  "message": "Video added successfully",
  "video": {
    "id": 1,
    "project_id": 1,
    "youtube_id": "dQw4w9WgXcQ",
    "youtube_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "status": "pending",
    "created_at": "2024-01-01T00:00:00"
  },
  "task_id": "celery-task-id"
}
```

### GET /videos/{video_id}
Get video details including transcript and segments.

**Response:**
```json
{
  "video": {
    "id": 1,
    "youtube_id": "dQw4w9WgXcQ",
    "title": "Video Title",
    "description": "Video description",
    "channel_name": "Channel Name",
    "duration": 212.5,
    "status": "ready",
    "license_type": "creative_commons",
    "is_republishable": true,
    "transcript": {
      "id": 1,
      "full_text": "Full transcript text...",
      "language": "en",
      "segments": [ /* timestamped segments */ ]
    },
    "segments": [
      {
        "id": 1,
        "start_time": 10.5,
        "end_time": 45.2,
        "duration": 34.7,
        "title": "Key Point 1",
        "description": "Important segment about...",
        "is_ai_suggested": true,
        "is_selected": false,
        "importance_score": 0.8,
        "topics": ["topic1", "topic2"]
      }
    ]
  }
}
```

### POST /videos/validate-url
Validate a YouTube URL without adding it to a project.

**Request Body:**
```json
{
  "youtube_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
}
```

**Response:**
```json
{
  "valid": true,
  "youtube_id": "dQw4w9WgXcQ",
  "message": "Valid YouTube URL"
}
```

## Processing Endpoints

### POST /processing/render
Start rendering the final video for a project.

**Request Body:**
```json
{
  "project_id": 1
}
```

**Response:**
```json
{
  "message": "Rendering started",
  "task_id": "celery-task-id",
  "project_status": "processing"
}
```

### GET /processing/status/{task_id}
Get the status of a processing task.

**Response:**
```json
{
  "state": "PROGRESS",
  "current": 50,
  "total": 100,
  "status": "Processing video segments..."
}
```

### GET /processing/project/{project_id}/download
Get download URL for a completed project.

**Response:**
```json
{
  "download_url": "https://storage.example.com/outputs/project_1.mp4",
  "filename": "My_Project_1.mp4",
  "duration": 180.5,
  "attribution": "Source videos: Video 1 by Channel 1..."
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error message description"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required or invalid
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- Authentication endpoints: 5 requests per minute
- General endpoints: 100 requests per minute
- File upload endpoints: 10 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Webhooks (Future)

UniDynamics will support webhooks for real-time notifications:

- Project completion
- Video processing status updates
- Error notifications

## SDK and Libraries

Official SDKs will be available for:
- JavaScript/TypeScript
- Python
- Go

## API Versioning

The API uses URL versioning:
- Current version: `/api/v1/`
- Future versions: `/api/v2/`, etc.

## Support

For API support:
- Documentation: https://docs.unidynamics.com
- GitHub Issues: https://github.com/unidynamics/unidynamics/issues
- Email: <EMAIL>
