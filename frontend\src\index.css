/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* Video.js custom styles */
.video-js {
  width: 100%;
  height: auto;
}

.video-js .vjs-big-play-button {
  font-size: 2.5em;
  line-height: 1.5em;
  height: 1.5em;
  width: 3em;
  border-radius: 0.3em;
  background-color: rgba(43, 51, 63, 0.7);
  border: 0.1em solid #fff;
  color: #fff;
}

/* Timeline styles */
.timeline-container {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-track {
  height: 60px;
  background: #f0f0f0;
  border-radius: 4px;
  margin: 8px 0;
  position: relative;
  overflow: hidden;
}

.timeline-segment {
  position: absolute;
  height: 100%;
  background: linear-gradient(45deg, #1976d2, #42a5f5);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.timeline-segment:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.timeline-segment.selected {
  background: linear-gradient(45deg, #dc004e, #ff5983);
}

.timeline-segment.ai-suggested {
  background: linear-gradient(45deg, #4caf50, #81c784);
}

/* Segment card styles */
.segment-card {
  border: 2px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
}

.segment-card:hover {
  border-color: #1976d2;
  transform: translateY(-2px);
}

.segment-card.selected {
  border-color: #dc004e;
  background-color: #fff5f5;
}

.segment-card.ai-suggested {
  border-left: 4px solid #4caf50;
}

/* Loading animations */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255,255,255,.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
  transition: width 0.3s ease;
}

/* Drag and drop styles */
.dropzone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropzone:hover,
.dropzone.active {
  border-color: #1976d2;
  background-color: #f3f8ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .timeline-segment {
    font-size: 10px;
    min-width: 20px;
  }
  
  .segment-card {
    margin-bottom: 8px;
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }
