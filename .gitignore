# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Flask
instance/
.webassets-cache

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# React build
frontend/build/
frontend/.eslintcache

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Uploads and temporary files
backend/uploads/
backend/temp/
*.tmp
*.temp

# AI Models (if stored locally)
models/
*.bin
*.safetensors

# Cloud credentials
*.json
*.pem
*.key
service-account*.json

# Testing
.coverage
.pytest_cache/
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebooks
.ipynb_checkpoints

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# Redis dump
dump.rdb

# FFmpeg temp files
*.ffmpeg_temp

# Video files (for development)
*.mp4
*.avi
*.mov
*.mkv
*.webm

# Audio files
*.wav
*.mp3
*.aac
*.flac

# Backup files
*.bak
*.backup

# Documentation build
docs/_build/
docs/site/

# Package files
*.tar.gz
*.zip
*.rar

# Local configuration overrides
config.local.py
docker-compose.override.yml
