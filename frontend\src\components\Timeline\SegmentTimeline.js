import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tooltip,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Divider
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as UncheckedIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

const SegmentTimeline = ({
  duration = 0,
  segments = [],
  selectedSegments = [],
  currentTime = 0,
  onSegmentToggle,
  onSegmentEdit,
  onSegmentDelete,
  onSeek,
  height = 120
}) => {
  const [hoveredSegment, setHoveredSegment] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [selectedSegmentForMenu, setSelectedSegmentForMenu] = useState(null);
  const timelineRef = useRef(null);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getSegmentColor = (segment) => {
    const isSelected = selectedSegments.some(s => s.id === segment.id);
    
    if (isSelected) return 'primary.main';
    
    // Color by importance or type
    if (segment.importance_score >= 0.8) return 'error.main';
    if (segment.importance_score >= 0.6) return 'warning.main';
    if (segment.importance_score >= 0.4) return 'info.main';
    return 'grey.400';
  };

  const getSegmentOpacity = (segment) => {
    const isSelected = selectedSegments.some(s => s.id === segment.id);
    return isSelected ? 0.9 : 0.7;
  };

  const handleTimelineClick = (event) => {
    if (!timelineRef.current || !onSeek) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const seekTime = percentage * duration;
    
    onSeek(Math.max(0, Math.min(duration, seekTime)));
  };

  const handleSegmentClick = (segment, event) => {
    event.stopPropagation();
    if (onSegmentToggle) {
      onSegmentToggle(segment);
    }
  };

  const handleSegmentRightClick = (segment, event) => {
    event.preventDefault();
    event.stopPropagation();
    setSelectedSegmentForMenu(segment);
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
    });
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
    setSelectedSegmentForMenu(null);
  };

  const handleEditSegment = () => {
    if (selectedSegmentForMenu && onSegmentEdit) {
      onSegmentEdit(selectedSegmentForMenu);
    }
    handleContextMenuClose();
  };

  const handleDeleteSegment = () => {
    if (selectedSegmentForMenu && onSegmentDelete) {
      onSegmentDelete(selectedSegmentForMenu);
    }
    handleContextMenuClose();
  };

  const currentTimePercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <Paper sx={{ p: 2, height }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h3">
          Video Timeline
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Duration: {formatTime(duration)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Current: {formatTime(currentTime)}
          </Typography>
        </Box>
      </Box>

      {/* Timeline Container */}
      <Box
        ref={timelineRef}
        onClick={handleTimelineClick}
        sx={{
          position: 'relative',
          height: 60,
          bgcolor: 'grey.100',
          borderRadius: 1,
          cursor: 'pointer',
          overflow: 'hidden'
        }}
      >
        {/* Time markers */}
        <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, height: '100%' }}>
          {Array.from({ length: Math.ceil(duration / 60) }, (_, i) => {
            const timeInSeconds = i * 60;
            const percentage = (timeInSeconds / duration) * 100;
            
            return (
              <Box
                key={i}
                sx={{
                  position: 'absolute',
                  left: `${percentage}%`,
                  top: 0,
                  bottom: 0,
                  width: 1,
                  bgcolor: 'grey.300',
                  '&::after': {
                    content: `"${formatTime(timeInSeconds)}"`,
                    position: 'absolute',
                    top: -20,
                    left: -15,
                    fontSize: '0.7rem',
                    color: 'text.secondary'
                  }
                }}
              />
            );
          })}
        </Box>

        {/* Segments */}
        {segments.map((segment, index) => {
          const startPercentage = (segment.start_time / duration) * 100;
          const widthPercentage = ((segment.end_time - segment.start_time) / duration) * 100;
          const isSelected = selectedSegments.some(s => s.id === segment.id);
          const isHovered = hoveredSegment?.id === segment.id;

          return (
            <Tooltip
              key={segment.id || index}
              title={
                <Box>
                  <Typography variant="subtitle2">
                    {segment.title || `Segment ${index + 1}`}
                  </Typography>
                  <Typography variant="body2">
                    {formatTime(segment.start_time)} - {formatTime(segment.end_time)}
                  </Typography>
                  <Typography variant="body2">
                    Score: {(segment.importance_score * 100).toFixed(0)}%
                  </Typography>
                  {segment.summary && (
                    <Typography variant="body2" sx={{ mt: 0.5, maxWidth: 200 }}>
                      {segment.summary}
                    </Typography>
                  )}
                </Box>
              }
              placement="top"
            >
              <Box
                onClick={(e) => handleSegmentClick(segment, e)}
                onContextMenu={(e) => handleSegmentRightClick(segment, e)}
                onMouseEnter={() => setHoveredSegment(segment)}
                onMouseLeave={() => setHoveredSegment(null)}
                sx={{
                  position: 'absolute',
                  left: `${startPercentage}%`,
                  width: `${widthPercentage}%`,
                  top: 10,
                  height: 40,
                  bgcolor: getSegmentColor(segment),
                  opacity: getSegmentOpacity(segment),
                  borderRadius: 1,
                  cursor: 'pointer',
                  border: isHovered ? 2 : 1,
                  borderColor: isHovered ? 'primary.dark' : 'transparent',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minWidth: 20,
                  '&:hover': {
                    opacity: 1,
                    transform: 'translateY(-2px)',
                    boxShadow: 2
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                {/* Selection indicator */}
                {isSelected ? (
                  <CheckCircleIcon sx={{ color: 'white', fontSize: 16 }} />
                ) : (
                  <UncheckedIcon sx={{ color: 'white', fontSize: 16 }} />
                )}
              </Box>
            </Tooltip>
          );
        })}

        {/* Current time indicator */}
        <Box
          sx={{
            position: 'absolute',
            left: `${currentTimePercentage}%`,
            top: 0,
            bottom: 0,
            width: 2,
            bgcolor: 'error.main',
            zIndex: 10,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: -5,
              left: -4,
              width: 10,
              height: 10,
              bgcolor: 'error.main',
              borderRadius: '50%'
            }
          }}
        />
      </Box>

      {/* Legend */}
      <Box sx={{ display: 'flex', gap: 2, mt: 2, flexWrap: 'wrap' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'primary.main', borderRadius: 0.5 }} />
          <Typography variant="caption">Selected</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'error.main', borderRadius: 0.5 }} />
          <Typography variant="caption">High Importance</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'warning.main', borderRadius: 0.5 }} />
          <Typography variant="caption">Medium Importance</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <Box sx={{ width: 12, height: 12, bgcolor: 'info.main', borderRadius: 0.5 }} />
          <Typography variant="caption">Low Importance</Typography>
        </Box>
      </Box>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={handleEditSegment}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Segment
        </MenuItem>
        <MenuItem onClick={handleDeleteSegment}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Segment
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default SegmentTimeline;
