import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Chip
} from '@mui/material';
import {
  DragIndicator as DragIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';

const MultiVideoTimeline = ({
  segments = [],
  videos = [],
  onSegmentReorder,
  onSegmentEdit,
  onSegmentDelete,
  onTransitionEdit,
  onSeek,
  totalDuration = 0,
  currentTime = 0
}) => {
  const [draggedSegment, setDraggedSegment] = useState(null);
  const [dropTarget, setDropTarget] = useState(null);
  const [contextMenu, setContextMenu] = useState(null);
  const [selectedSegment, setSelectedSegment] = useState(null);
  const [transitionDialog, setTransitionDialog] = useState(false);
  const [transitionSettings, setTransitionSettings] = useState({
    type: 'cut',
    duration: 0.5
  });
  const timelineRef = useRef(null);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getVideoColor = (videoId) => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    return colors[videoId % colors.length];
  };

  const getVideoName = (videoId) => {
    const video = videos.find(v => v.id === videoId);
    return video ? video.title || `Video ${videoId}` : `Video ${videoId}`;
  };

  const handleDragStart = (e, segment) => {
    setDraggedSegment(segment);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e, targetSegment) => {
    e.preventDefault();
    if (draggedSegment && targetSegment.id !== draggedSegment.id) {
      setDropTarget(targetSegment);
    }
  };

  const handleDrop = (e, targetSegment) => {
    e.preventDefault();
    if (draggedSegment && targetSegment && onSegmentReorder) {
      onSegmentReorder(draggedSegment.id, targetSegment.timeline_position);
    }
    setDraggedSegment(null);
    setDropTarget(null);
  };

  const handleContextMenu = (e, segment) => {
    e.preventDefault();
    setSelectedSegment(segment);
    setContextMenu({
      mouseX: e.clientX - 2,
      mouseY: e.clientY - 4,
    });
  };

  const handleContextMenuClose = () => {
    setContextMenu(null);
    setSelectedSegment(null);
  };

  const handleEditTransition = () => {
    if (selectedSegment) {
      setTransitionSettings({
        type: selectedSegment.transition_in || 'cut',
        duration: selectedSegment.transition_in_duration || 0.5
      });
      setTransitionDialog(true);
    }
    handleContextMenuClose();
  };

  const handleSaveTransition = () => {
    if (selectedSegment && onTransitionEdit) {
      onTransitionEdit(selectedSegment.id, transitionSettings);
    }
    setTransitionDialog(false);
    setSelectedSegment(null);
  };

  const handleTimelineClick = (e) => {
    if (!timelineRef.current || !onSeek) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const seekTime = percentage * totalDuration;
    
    onSeek(Math.max(0, Math.min(totalDuration, seekTime)));
  };

  // Calculate timeline positions
  let currentPosition = 0;
  const timelineSegments = segments.map((segment, index) => {
    const startPosition = currentPosition;
    const endPosition = currentPosition + segment.duration;
    currentPosition = endPosition;
    
    return {
      ...segment,
      timelineStart: startPosition,
      timelineEnd: endPosition,
      widthPercentage: (segment.duration / totalDuration) * 100,
      leftPercentage: (startPosition / totalDuration) * 100
    };
  });

  const currentTimePercentage = totalDuration > 0 ? (currentTime / totalDuration) * 100 : 0;

  return (
    <Paper sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h3">
          Multi-Video Timeline
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Total: {formatTime(totalDuration)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Segments: {segments.length}
          </Typography>
        </Box>
      </Box>

      {/* Video Legend */}
      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
        {videos.map((video) => (
          <Chip
            key={video.id}
            label={video.title || `Video ${video.id}`}
            size="small"
            sx={{
              bgcolor: getVideoColor(video.id),
              color: 'white',
              '& .MuiChip-label': { fontWeight: 500 }
            }}
          />
        ))}
      </Box>

      {/* Timeline Container */}
      <Box
        ref={timelineRef}
        onClick={handleTimelineClick}
        sx={{
          position: 'relative',
          height: 80,
          bgcolor: 'grey.100',
          borderRadius: 1,
          cursor: 'pointer',
          overflow: 'hidden',
          mb: 2
        }}
      >
        {/* Time markers */}
        {Array.from({ length: Math.ceil(totalDuration / 30) }, (_, i) => {
          const timeInSeconds = i * 30;
          const percentage = (timeInSeconds / totalDuration) * 100;
          
          return (
            <Box
              key={i}
              sx={{
                position: 'absolute',
                left: `${percentage}%`,
                top: 0,
                bottom: 0,
                width: 1,
                bgcolor: 'grey.300',
                '&::after': {
                  content: `"${formatTime(timeInSeconds)}"`,
                  position: 'absolute',
                  top: -20,
                  left: -15,
                  fontSize: '0.7rem',
                  color: 'text.secondary'
                }
              }}
            />
          );
        })}

        {/* Segments */}
        {timelineSegments.map((segment, index) => (
          <Box
            key={segment.id}
            draggable
            onDragStart={(e) => handleDragStart(e, segment)}
            onDragOver={handleDragOver}
            onDragEnter={(e) => handleDragEnter(e, segment)}
            onDrop={(e) => handleDrop(e, segment)}
            onContextMenu={(e) => handleContextMenu(e, segment)}
            sx={{
              position: 'absolute',
              left: `${segment.leftPercentage}%`,
              width: `${segment.widthPercentage}%`,
              top: 10,
              height: 60,
              bgcolor: getVideoColor(segment.video_id),
              borderRadius: 1,
              cursor: 'grab',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              px: 1,
              border: dropTarget?.id === segment.id ? 2 : 1,
              borderColor: dropTarget?.id === segment.id ? 'primary.main' : 'transparent',
              opacity: draggedSegment?.id === segment.id ? 0.5 : 1,
              '&:hover': {
                opacity: 0.9,
                transform: 'translateY(-2px)',
                boxShadow: 2
              },
              transition: 'all 0.2s ease'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, flex: 1, minWidth: 0 }}>
              <DragIcon sx={{ color: 'white', fontSize: 16 }} />
              <Typography 
                variant="caption" 
                sx={{ 
                  color: 'white', 
                  fontWeight: 500,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {segment.title || `Segment ${index + 1}`}
              </Typography>
            </Box>
            
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleContextMenu(e, segment);
              }}
              sx={{ color: 'white', p: 0.5 }}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Box>
        ))}

        {/* Current time indicator */}
        <Box
          sx={{
            position: 'absolute',
            left: `${currentTimePercentage}%`,
            top: 0,
            bottom: 0,
            width: 2,
            bgcolor: 'error.main',
            zIndex: 10,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: -5,
              left: -4,
              width: 10,
              height: 10,
              bgcolor: 'error.main',
              borderRadius: '50%'
            }
          }}
        />
      </Box>

      {/* Segment Details */}
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {timelineSegments.map((segment, index) => (
          <Tooltip
            key={segment.id}
            title={
              <Box>
                <Typography variant="subtitle2">
                  {segment.title || `Segment ${index + 1}`}
                </Typography>
                <Typography variant="body2">
                  Source: {getVideoName(segment.video_id)}
                </Typography>
                <Typography variant="body2">
                  Duration: {formatTime(segment.duration)}
                </Typography>
                <Typography variant="body2">
                  Transition: {segment.transition_in || 'cut'}
                </Typography>
              </Box>
            }
          >
            <Chip
              label={`${index + 1}`}
              size="small"
              sx={{
                bgcolor: getVideoColor(segment.video_id),
                color: 'white',
                minWidth: 32
              }}
            />
          </Tooltip>
        ))}
      </Box>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem onClick={() => onSeek && onSeek(selectedSegment?.start_time || 0)}>
          <PlayIcon sx={{ mr: 1 }} />
          Play from Start
        </MenuItem>
        <MenuItem onClick={handleEditTransition}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Transition
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedSegment && onSegmentEdit) {
            onSegmentEdit(selectedSegment);
          }
          handleContextMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Segment
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedSegment && onSegmentDelete) {
            onSegmentDelete(selectedSegment.id);
          }
          handleContextMenuClose();
        }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Remove from Timeline
        </MenuItem>
      </Menu>

      {/* Transition Edit Dialog */}
      <Dialog open={transitionDialog} onClose={() => setTransitionDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Transition</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Transition Type</InputLabel>
              <Select
                value={transitionSettings.type}
                label="Transition Type"
                onChange={(e) => setTransitionSettings(prev => ({ ...prev, type: e.target.value }))}
              >
                <MenuItem value="cut">Cut</MenuItem>
                <MenuItem value="fade">Fade</MenuItem>
                <MenuItem value="dissolve">Dissolve</MenuItem>
              </Select>
            </FormControl>
            
            {transitionSettings.type !== 'cut' && (
              <TextField
                fullWidth
                label="Duration (seconds)"
                type="number"
                value={transitionSettings.duration}
                onChange={(e) => setTransitionSettings(prev => ({ ...prev, duration: parseFloat(e.target.value) }))}
                inputProps={{ min: 0.1, max: 5.0, step: 0.1 }}
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTransitionDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveTransition} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default MultiVideoTimeline;
