import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  CircularProgress,
  Chip,
  Divider,
  IconButton,
  Toolt<PERSON>
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';

import VideoPlayer from '../../components/VideoPlayer/VideoPlayer';
import TranscriptViewer from '../../components/Transcript/TranscriptViewer';
import SegmentTimeline from '../../components/Timeline/SegmentTimeline';
import SegmentSelector from '../../components/Segments/SegmentSelector';
import useVideoProcessing from '../../hooks/useVideoProcessing';

const VideoEditorPage = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();

  // Video processing hook
  const {
    project,
    videos,
    selectedVideo,
    segments,
    selectedSegments,
    transcript,
    processingStatus,
    loading,
    error,
    setSelectedVideo,
    addVideo,
    toggleSegment,
    selectAllSegments,
    selectNoSegments,
    selectSegmentsByImportance,
    regenerateSegments,
    startRendering,
    getDownloadUrl,
    setError
  } = useVideoProcessing(projectId);

  // Local state
  const [currentTime, setCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);
  const [addVideoDialogOpen, setAddVideoDialogOpen] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleAddVideo = async () => {
    if (!youtubeUrl.trim()) {
      showSnackbar('Please enter a YouTube URL', 'error');
      return;
    }

    try {
      await addVideo(youtubeUrl);
      setYoutubeUrl('');
      setAddVideoDialogOpen(false);
      showSnackbar('Video added successfully! Processing will begin shortly.', 'success');
    } catch (err) {
      showSnackbar(err.response?.data?.error || 'Failed to add video', 'error');
    }
  };

  const handleSeek = (time) => {
    setCurrentTime(time);
  };

  const handleVideoMetadata = (metadata) => {
    setVideoDuration(metadata.duration);
  };

  const handleRegenerateSegments = async () => {
    try {
      await regenerateSegments();
      showSnackbar('Segments regenerated successfully!', 'success');
    } catch (err) {
      showSnackbar('Failed to regenerate segments', 'error');
    }
  };

  const handleStartRendering = async () => {
    if (selectedSegments.length === 0) {
      showSnackbar('Please select at least one segment to render', 'warning');
      return;
    }

    try {
      await startRendering();
      showSnackbar('Rendering started! You will be notified when complete.', 'success');
    } catch (err) {
      showSnackbar('Failed to start rendering', 'error');
    }
  };

  const handleDownload = async () => {
    try {
      const downloadData = await getDownloadUrl();
      if (downloadData?.download_url) {
        window.open(downloadData.download_url, '_blank');
      }
    } catch (err) {
      showSnackbar('Failed to get download URL', 'error');
    }
  };

  const getVideoStatus = (video) => {
    const status = processingStatus[video.id];
    if (!status) return video.status || 'pending';
    return status.status;
  };

  const getVideoStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  if (loading && !project) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !project) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={() => navigate('/dashboard')}>
              <ArrowBackIcon />
            </IconButton>
            <Box>
              <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
                {project?.title || 'Video Editor'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {project?.description || `Project ID: ${projectId}`}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {/* Project Status */}
            <Chip
              label={project?.status || 'draft'}
              color={project?.status === 'completed' ? 'success' : 'default'}
              variant="outlined"
            />

            {/* Action Buttons */}
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setAddVideoDialogOpen(true)}
            >
              Add Video
            </Button>

            {segments.length > 0 && (
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRegenerateSegments}
                disabled={loading}
              >
                Regenerate Segments
              </Button>
            )}

            {selectedSegments.length > 0 && (
              <Button
                variant="contained"
                startIcon={<PlayIcon />}
                onClick={handleStartRendering}
                disabled={loading || project?.status === 'processing'}
              >
                Render Video
              </Button>
            )}

            {project?.status === 'completed' && (
              <Button
                variant="contained"
                color="success"
                startIcon={<DownloadIcon />}
                onClick={handleDownload}
              >
                Download
              </Button>
            )}
          </Box>
        </Box>

        {/* Video Selection */}
        {videos.length > 0 && (
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Typography variant="body2" sx={{ alignSelf: 'center', mr: 1 }}>
              Videos:
            </Typography>
            {videos.map((video) => (
              <Chip
                key={video.id}
                label={video.title || `Video ${video.id}`}
                color={selectedVideo?.id === video.id ? 'primary' : 'default'}
                variant={selectedVideo?.id === video.id ? 'filled' : 'outlined'}
                onClick={() => setSelectedVideo(video)}
                onDelete={getVideoStatus(video) === 'processing' ? undefined : null}
                deleteIcon={
                  <Chip
                    label={getVideoStatus(video)}
                    color={getVideoStatusColor(getVideoStatus(video))}
                    size="small"
                  />
                }
              />
            ))}
          </Box>
        )}
      </Paper>

      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {videos.length === 0 ? (
          // No videos state
          <Paper sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Typography variant="h6" gutterBottom>
              No videos added yet
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Add a YouTube video to get started with creating your summary.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setAddVideoDialogOpen(true)}
              sx={{ alignSelf: 'center' }}
            >
              Add Your First Video
            </Button>
          </Paper>
        ) : !selectedVideo ? (
          // No selected video state
          <Paper sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Typography variant="h6" gutterBottom>
              Select a video to edit
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Choose a video from the list above to start editing.
            </Typography>
          </Paper>
        ) : (
          // Main editor interface
          <Grid container spacing={2} sx={{ height: '100%' }}>
            {/* Left Column - Video Player and Timeline */}
            <Grid item xs={12} md={8} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* Video Player */}
              <Box sx={{ mb: 2 }}>
                <VideoPlayer
                  src={selectedVideo.file_url}
                  onTimeUpdate={setCurrentTime}
                  onLoadedMetadata={handleVideoMetadata}
                  currentTime={currentTime}
                  segments={segments}
                  selectedSegments={selectedSegments}
                  onSegmentClick={(segment) => handleSeek(segment.start_time)}
                />
              </Box>

              {/* Timeline */}
              <Box sx={{ mb: 2 }}>
                <SegmentTimeline
                  duration={videoDuration}
                  segments={segments}
                  selectedSegments={selectedSegments}
                  currentTime={currentTime}
                  onSegmentToggle={toggleSegment}
                  onSeek={handleSeek}
                  height={120}
                />
              </Box>

              {/* Transcript */}
              <Box sx={{ flex: 1, minHeight: 300 }}>
                <TranscriptViewer
                  transcript={transcript}
                  currentTime={currentTime}
                  onSeek={handleSeek}
                  segments={segments}
                  selectedSegments={selectedSegments}
                  onSegmentToggle={toggleSegment}
                  height={300}
                />
              </Box>
            </Grid>

            {/* Right Column - Segment Selector */}
            <Grid item xs={12} md={4} sx={{ height: '100%' }}>
              <SegmentSelector
                segments={segments}
                selectedSegments={selectedSegments}
                onSegmentToggle={toggleSegment}
                onSeek={handleSeek}
                onSelectAll={selectAllSegments}
                onSelectNone={selectNoSegments}
                onSelectByImportance={selectSegmentsByImportance}
              />
            </Grid>
          </Grid>
        )}
      </Box>

      {/* Add Video Dialog */}
      <Dialog open={addVideoDialogOpen} onClose={() => setAddVideoDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add YouTube Video</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="YouTube URL"
            type="url"
            fullWidth
            variant="outlined"
            value={youtubeUrl}
            onChange={(e) => setYoutubeUrl(e.target.value)}
            placeholder="https://www.youtube.com/watch?v=..."
            helperText="Enter a YouTube video URL. The video will be downloaded and processed automatically."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddVideoDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddVideo} variant="contained" disabled={!youtubeUrl.trim()}>
            Add Video
          </Button>
        </DialogActions>
      </Dialog>

      {/* Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Global Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default VideoEditorPage;
