# UniDynamics - Task Breakdown

This document outlines the tasks required to build UniDynamics, aligned with the phases in `PLANNING.MD`.

## Phase 0: Setup & Foundational Work

### Project Management & General
- [ ] Define detailed requirements for Phase 1 (MVP).
- [ ] Set up project management board (e.g., Jira, Trello, GitHub Projects).
- [ ] Establish version control (Git repository on GitHub/GitLab).
- [ ] Set up communication channels (e.g., Slack, Teams).
- [ ] Outline initial CI/CD pipeline (e.g., GitHub Actions for linting, basic tests).

### Technology & Infrastructure
- [ ] Finalize choices for Frontend framework (React/Vue).
- [ ] Finalize choices for Backend framework (Python/Node.js).
- [ ] Finalize choice for Database (PostgreSQL).
- [ ] **Finalize choice and setup for Transcription:**
    - [ ] Evaluate self-hosting OpenAI Whisper (setup, GPU requirements) vs. Whisper API vs. AssemblyAI.
    - [ ] If self-hosting Whisper, set up the necessary environment and model.
- [ ] **Setup for Local LLM with Ollama:**
    - [ ] Install Ollama on a development/staging machine (or a designated server/VM).
    - [ ] Download and test desired LLMs via Ollama (e.g., Llama 2, Mistral for summarization).
    - [ ] Verify API accessibility of Ollama.
- [ ] Finalize choice for Task Queue (Celery + Broker).
- [ ] Set up basic cloud infrastructure:
    - [ ] Cloud account (AWS/GCP/Azure).
    - [ ] Object storage bucket (S3/GCS) for development.
    - [ ] Development database instance.
    - [ ] Basic server/container setup for backend API (dev environment).
        - [ ] *Consider GPU instance if self-hosting Whisper and/or running Ollama on the same dev/staging cloud VM.*
    - [ ] Static hosting for frontend (dev environment).

## Phase 1: MVP - Core Summarizer & Trimmer (Single Video Focus)

### Backend Development
- [ ] **User Authentication:**
    - [ ] Design database schema for users.
    - [ ] Implement registration (email/password).
    - [ ] Implement login and session management (JWT).
    - [ ] (Optional) Implement "Sign in with Google" (OAuth 2.0).
- [ ] **YouTube Integration:**
    - [ ] API endpoint to accept YouTube URL.
    - [ ] Integrate YouTube Data API client.
    - [ ] Fetch video metadata (title, description, license).
    - [ ] Implement license check (filter for Creative Commons, etc.).
- [ ] **Video Downloading:**
    - [ ] Integrate `yt-dlp` for downloading videos.
    - [ ] Task queue job for asynchronous video download to cloud storage.
- [ ] **Transcription:**
    - [ ] Integrate chosen transcription solution (Self-hosted Whisper, Whisper API, or AssemblyAI).
    - [ ] Task queue job for asynchronous video transcription.
    - [ ] Store transcript (e.g., JSON with timestamps) in database or cloud storage.
- [ ] **AI Segment Suggestion (Leveraging Ollama):**
    - [ ] Design prompts for the selected Ollama LLM to:
        - [ ] Identify key topics/themes in the transcript.
        - [ ] Suggest important sentences or paragraphs.
        - [ ] Extract potential start/end timestamps based on transcript structure.
    - [ ] Implement LangChain (or direct API calls) to interact with the local Ollama API using the designed prompts.
    - [ ] Process LLM output to derive structured segment suggestions.
    - [ ] API endpoint to provide suggested segments for a video.
- [ ] **Video Processing (Core):**
    - [ ] API endpoint to receive selected segments (start/end times) from user.
    - [ ] Integrate FFmpeg/MoviePy for:
        - [ ] Trimming video based on timestamps.
        - [ ] Concatenating selected segments.
    - [ ] Task queue job for asynchronous video rendering.
    - [ ] Store final output video in cloud storage.
- [ ] **Attribution:**
    - [ ] Logic to generate basic attribution text based on source video metadata.
- [ ] **API Endpoints & Core Logic:**
    - [ ] CRUD operations for user projects/videos.
    - [ ] API for job status polling (download, transcribe, render).
    - [ ] API to serve download link for final video.
- [ ] **Database Schema:**
    - [ ] Design and implement schema for users, projects, videos, transcripts, segments.

### Frontend Development
- [ ] **Setup & Basic Layout:**
    - [ ] Initialize Frontend project (React/Vue).
    - [ ] Basic application layout (header, navigation, main content area).
- [ ] **User Authentication UI:**
    - [ ] Registration form.
    - [ ] Login form.
    - [ ] Handle authentication state.
- [ ] **Dashboard UI:**
    - [ ] List user's projects/videos.
    - [ ] Button to start new project/add video.
- [ ] **Video Input UI:**
    - [ ] Form to input YouTube URL.
    - [ ] Display video metadata after fetching.
    - [ ] Display license information and warnings.
- [ ] **Summarization & Editing UI (Single Video):**
    - [ ] Display video player for previewing source video.
    - [ ] Display full transcript.
    - [ ] Highlight AI-suggested segments on transcript or timeline (data sourced from Ollama via backend).
    - [ ] Allow user to select/deselect segments.
    - [ ] Allow user to manually adjust start/end times of segments.
    - [ ] "Preview" button for selected segments (optional, could be just "Process").
    - [ ] "Process Video" button to send selections to backend.
- [ ] **Output & Download UI:**
    - [ ] Display processing status.
    - [ ] Display generated attribution text.
    - [ ] Download button for the final video.
- [ ] **API Integration:**
    - [ ] Connect frontend components to all backend APIs.
    - [ ] Error handling and user feedback for API calls.

### Testing & Deployment (MVP)
- [ ] Unit tests for critical backend logic (auth, processing triggers, Ollama integration).
- [ ] Basic E2E tests for core user flow (submit URL -> get output).
- [ ] Manual testing of all MVP features, including Ollama-driven suggestions.
- [ ] Prepare deployment scripts for backend and frontend to staging/production.
- [ ] Initial deployment of MVP.

## Phase 2: Multi-Video Compilation & Basic Enhancements

### Backend Development
- [ ] **Multi-Video Handling:**
    - [ ] Modify project/video schema to support multiple source videos per project.
    - [ ] API endpoints to manage multiple source videos in a project.
- [ ] **Video Processing (Multi-Video):**
    - [ ] Update FFmpeg/MoviePy logic to concatenate segments from *different* source files.
    - [ ] Implement simple transitions (cuts, fades) between clips.
- [ ] **Intro/Outro Logic:**
    - [ ] API to manage user-uploaded intro/outro clips.
    - [ ] Modify rendering process to prepend/append intro/outro.
- [ ] **Cloud Rendering Queue Optimization:**
    - [ ] Refine task queue for potentially longer/more complex jobs.

### Frontend Development
- [ ] **Multi-Video Input UI:**
    - [ ] Allow adding multiple YouTube URLs to a single project.
- [ ] **Multi-Track Timeline UI:**
    - [ ] Design and implement a timeline view showing tracks for each source video.
    - [ ] Display selected/suggested clips on their respective source tracks.
    - [ ] Master timeline for sequencing clips from various sources.
    - [ ] Drag-and-drop functionality to arrange clips on the master timeline.
- [ ] **Transitions UI:**
    - [ ] Simple UI to select transitions between clips on the master timeline.
- [ ] **Intro/Outro UI:**
    - [ ] UI to upload or select pre-set intro/outro sequences.
    - [ ] UI to assign intro/outro to the project.

### Testing & Deployment (Phase 2)
- [ ] Unit and integration tests for multi-video logic.
- [ ] E2E tests for multi-video compilation flow.
- [ ] Manual testing of all Phase 2 features.
- [ ] Deploy Phase 2 updates.

## Phase 3: Advanced AI & Feature Enrichment

### Backend Development
- [ ] **Advanced NLP & Summarization (with Ollama and others):**
    - [ ] Experiment with different local LLMs via Ollama for abstractive summarization.
    - [ ] Fine-tune prompts for Ollama LLMs for better quality summarization and topic modeling.
    - [ ] (Optional) Integrate Hugging Face Transformers for specific tasks or models not available/performant via Ollama.
    - [ ] (Optional) Use OpenAI API as a fallback or for comparative quality if local models struggle with certain content.
- [ ] **Visual Cue Analysis (Optional/Advanced):**
    - [ ] Integrate OpenCV or cloud video AI (Rekognition/Video Intelligence API) for scene detection.
    - [ ] Correlate visual cues with transcript data.
- [ ] **Text Overlay Logic:**
    - [ ] Modify FFmpeg commands to add text overlays with timing.
    - [ ] API to define text content, position, and timing.
- [ ] **Background Music Logic:**
    - [ ] API to select royalty-free tracks.
    - [ ] Modify rendering to mix background audio.
- [ ] **Chapter Generation Logic:**
    - [ ] Generate chapter markers based on combined segments.
- [ ] **Direct YouTube Re-upload:**
    - [ ] Implement YouTube API OAuth for channel access.
    - [ ] API to trigger upload of final video with metadata.
- [ ] **AI Metadata Generation (with Ollama):**
    - [ ] Design prompts for Ollama LLMs to suggest titles, descriptions, and tags based on video content/transcript.
    - [ ] Integrate LangChain/API calls to Ollama for metadata generation.

### Frontend Development
- [ ] **Advanced AI Settings UI:**
    - [ ] Options to select different summarization models/aggressiveness (if backend supports multiple Ollama models or fallbacks).
- [ ] **Text Overlay Editor UI:**
    - [ ] UI to add and position text on video segments.
    - [ ] Preview of text overlays.
- [ ] **Background Music Selection UI:**
    - [ ] Browse and select royalty-free tracks.
- [ ] **YouTube Re-upload UI:**
    - [ ] Authenticate with YouTube.
    - [ ] Form to input/edit title, description, tags (with AI suggestions from Ollama via backend).
    - [ ] Upload progress.

### Testing & Deployment (Phase 3)
- [ ] Comprehensive testing for all advanced features.
- [ ] Performance testing for more complex AI models/Ollama interactions.
- [ ] Security review, especially for YouTube OAuth.
- [ ] Deploy Phase 3 updates.

## Ongoing / Cross-Cutting Tasks

- [ ] **Documentation:**
    - [ ] User documentation/guides.
    - [ ] API documentation (Swagger/OpenAPI).
    - [ ] Technical documentation for developers (including Ollama setup & interaction).
- [ ] **Monitoring & Logging:**
    - [ ] Set up application performance monitoring (APM).
    - [ ] Centralized logging for backend services, including Ollama interactions.
- [ ] **Error Handling & Reporting:**
    - [ ] Implement robust error handling across the stack.
    - [ ] Set up error reporting service (e.g., Sentry).
- [ ] **Security:**
    - [ ] Regular security audits and updates.
    - [ ] Input validation, protection against common web vulnerabilities.
- [ ] **User Feedback & Iteration:**
    - [ ] Collect user feedback.
    - [ ] Plan and implement improvements based on feedback.
- [ ] **Cost Optimization:**
    - [ ] Continuously monitor and optimize cloud resource usage (especially if using cloud VMs for Ollama/Whisper).
    - [ ] Evaluate performance vs. cost for different local LLMs.
- [ ] **Legal & Compliance:**
    - [ ] Ensure ongoing compliance with YouTube ToS, CC licenses, data privacy (GDPR/CCPA).

This task list is comprehensive and should be broken down further into smaller tasks within a project management tool for sprints or iterations. Prioritization will be key, especially for the MVP.