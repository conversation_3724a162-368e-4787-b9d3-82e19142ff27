version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: unidynamics
      POSTGRES_USER: unidynamics
      POSTGRES_PASSWORD: unidynamics_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U unidynamics"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=***********************************************************/unidynamics
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OLLAMA_BASE_URL=http://ollama:11434
    ports:
      - "5000:5000"
    volumes:
      - ../backend:/app
      - uploads_data:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: python run.py run --host=0.0.0.0 --port=5000

  # Celery Worker
  celery-worker:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=***********************************************************/unidynamics
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OLLAMA_BASE_URL=http://ollama:11434
    volumes:
      - ../backend:/app
      - uploads_data:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.celery worker --loglevel=info

  # Ollama for Local LLMs
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_KEEP_ALIVE=24h
    # Note: You'll need to pull models manually after starting
    # docker exec -it unidynamics-ollama-1 ollama pull llama2

  # Frontend (Development)
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:5000/api
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    command: npm start

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  uploads_data:
