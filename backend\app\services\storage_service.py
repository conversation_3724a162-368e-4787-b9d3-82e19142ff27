import os
import boto3
from google.cloud import storage as gcs
from flask import current_app
from botocore.exceptions import ClientError


class StorageService:
    """Service for cloud storage operations (AWS S3 / Google Cloud Storage)."""

    def __init__(self):
        # Check if we should use local storage for development
        if current_app.config.get('USE_LOCAL_STORAGE', False):
            self.storage_type = 'local'
        else:
            self.storage_type = self._determine_storage_type()

        if self.storage_type == 'aws':
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=current_app.config.get('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=current_app.config.get('AWS_SECRET_ACCESS_KEY'),
                region_name=current_app.config.get('AWS_REGION')
            )
            self.bucket_name = current_app.config.get('S3_BUCKET')

        elif self.storage_type == 'gcs':
            credentials_path = current_app.config.get('GOOGLE_APPLICATION_CREDENTIALS')
            if credentials_path:
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path

            self.gcs_client = gcs.Client(
                project=current_app.config.get('GOOGLE_CLOUD_PROJECT')
            )
            self.bucket_name = current_app.config.get('GCS_BUCKET')

        else:
            current_app.logger.warning("No cloud storage configured")

    def _determine_storage_type(self):
        """Determine which storage service to use based on configuration."""
        if current_app.config.get('S3_BUCKET') and current_app.config.get('AWS_ACCESS_KEY_ID'):
            return 'aws'
        elif current_app.config.get('GCS_BUCKET') and current_app.config.get('GOOGLE_CLOUD_PROJECT'):
            return 'gcs'
        else:
            return None

    def upload_file(self, local_file_path, remote_file_path):
        """Upload file to cloud storage or store locally."""
        try:
            if self.storage_type == 'local':
                return self._store_locally(local_file_path, remote_file_path)
            elif self.storage_type == 'aws':
                return self._upload_to_s3(local_file_path, remote_file_path)
            elif self.storage_type == 'gcs':
                return self._upload_to_gcs(local_file_path, remote_file_path)
            else:
                # Fallback to local storage for development
                current_app.logger.warning("No cloud storage configured, using local storage")
                return self._store_locally(local_file_path, remote_file_path)

        except Exception as e:
            current_app.logger.error(f"Error uploading file: {e}")
            raise

    def _upload_to_s3(self, local_file_path, remote_file_path):
        """Upload file to AWS S3."""
        try:
            self.s3_client.upload_file(
                local_file_path,
                self.bucket_name,
                remote_file_path,
                ExtraArgs={'ACL': 'private'}
            )

            # Generate URL
            url = f"https://{self.bucket_name}.s3.{current_app.config.get('AWS_REGION')}.amazonaws.com/{remote_file_path}"
            return url

        except ClientError as e:
            current_app.logger.error(f"S3 upload error: {e}")
            raise

    def _upload_to_gcs(self, local_file_path, remote_file_path):
        """Upload file to Google Cloud Storage."""
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(remote_file_path)

            blob.upload_from_filename(local_file_path)

            # Generate URL
            url = f"gs://{self.bucket_name}/{remote_file_path}"
            return url

        except Exception as e:
            current_app.logger.error(f"GCS upload error: {e}")
            raise

    def download_file(self, remote_url, local_file_path):
        """Download file from cloud storage."""
        try:
            if self.storage_type == 'aws':
                return self._download_from_s3(remote_url, local_file_path)
            elif self.storage_type == 'gcs':
                return self._download_from_gcs(remote_url, local_file_path)
            else:
                raise Exception("No storage service configured")

        except Exception as e:
            current_app.logger.error(f"Error downloading file: {e}")
            raise

    def _download_from_s3(self, remote_url, local_file_path):
        """Download file from AWS S3."""
        try:
            # Extract key from URL
            key = remote_url.split(f"{self.bucket_name}.s3.")[-1].split("/", 1)[-1]

            self.s3_client.download_file(
                self.bucket_name,
                key,
                local_file_path
            )

            return local_file_path

        except ClientError as e:
            current_app.logger.error(f"S3 download error: {e}")
            raise

    def _download_from_gcs(self, remote_url, local_file_path):
        """Download file from Google Cloud Storage."""
        try:
            # Extract blob name from URL
            blob_name = remote_url.replace(f"gs://{self.bucket_name}/", "")

            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(blob_name)

            blob.download_to_filename(local_file_path)

            return local_file_path

        except Exception as e:
            current_app.logger.error(f"GCS download error: {e}")
            raise

    def delete_file(self, remote_url):
        """Delete file from cloud storage."""
        try:
            if self.storage_type == 'aws':
                return self._delete_from_s3(remote_url)
            elif self.storage_type == 'gcs':
                return self._delete_from_gcs(remote_url)
            else:
                raise Exception("No storage service configured")

        except Exception as e:
            current_app.logger.error(f"Error deleting file: {e}")
            raise

    def _delete_from_s3(self, remote_url):
        """Delete file from AWS S3."""
        try:
            # Extract key from URL
            key = remote_url.split(f"{self.bucket_name}.s3.")[-1].split("/", 1)[-1]

            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=key
            )

            return True

        except ClientError as e:
            current_app.logger.error(f"S3 delete error: {e}")
            raise

    def _delete_from_gcs(self, remote_url):
        """Delete file from Google Cloud Storage."""
        try:
            # Extract blob name from URL
            blob_name = remote_url.replace(f"gs://{self.bucket_name}/", "")

            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(blob_name)

            blob.delete()

            return True

        except Exception as e:
            current_app.logger.error(f"GCS delete error: {e}")
            raise

    def generate_presigned_url(self, remote_url, expiration=3600):
        """Generate a presigned URL for temporary access."""
        try:
            if self.storage_type == 'aws':
                return self._generate_s3_presigned_url(remote_url, expiration)
            elif self.storage_type == 'gcs':
                return self._generate_gcs_presigned_url(remote_url, expiration)
            else:
                return remote_url  # Fallback to original URL

        except Exception as e:
            current_app.logger.error(f"Error generating presigned URL: {e}")
            return remote_url  # Fallback to original URL

    def _generate_s3_presigned_url(self, remote_url, expiration):
        """Generate presigned URL for S3."""
        try:
            # Extract key from URL
            key = remote_url.split(f"{self.bucket_name}.s3.")[-1].split("/", 1)[-1]

            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': key},
                ExpiresIn=expiration
            )

            return url

        except ClientError as e:
            current_app.logger.error(f"S3 presigned URL error: {e}")
            raise

    def _generate_gcs_presigned_url(self, remote_url, expiration):
        """Generate signed URL for GCS."""
        try:
            from datetime import datetime, timedelta

            # Extract blob name from URL
            blob_name = remote_url.replace(f"gs://{self.bucket_name}/", "")

            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(blob_name)

            url = blob.generate_signed_url(
                expiration=datetime.utcnow() + timedelta(seconds=expiration),
                method='GET'
            )

            return url

        except Exception as e:
            current_app.logger.error(f"GCS signed URL error: {e}")
            raise

    def _store_locally(self, local_file_path, remote_file_path):
        """Store file locally for development (fallback when no cloud storage configured)."""
        try:
            import shutil

            # Create local storage directory
            local_storage_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'storage')
            os.makedirs(local_storage_dir, exist_ok=True)

            # Create destination path
            dest_path = os.path.join(local_storage_dir, remote_file_path.replace('/', os.sep))
            dest_dir = os.path.dirname(dest_path)
            os.makedirs(dest_dir, exist_ok=True)

            # Copy file
            shutil.copy2(local_file_path, dest_path)

            # Return local file URL
            return f"file://{dest_path}"

        except Exception as e:
            current_app.logger.error(f"Local storage error: {e}")
            raise
