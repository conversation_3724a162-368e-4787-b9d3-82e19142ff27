#!/usr/bin/env python3
"""
Minimal startup script for UniDynamics backend
This script starts the backend with only essential features
"""

import os
import sys
from flask import Flask, jsonify

def create_minimal_app():
    """Create a minimal Flask app for testing."""
    app = Flask(__name__)
    
    # Basic configuration
    app.config['SECRET_KEY'] = 'dev-secret-key'
    app.config['JWT_SECRET_KEY'] = 'jwt-secret-key'
    
    @app.route('/health')
    def health():
        return jsonify({
            'status': 'healthy',
            'message': 'UniDynamics Backend is running (minimal mode)',
            'version': '1.0.0'
        })
    
    @app.route('/api/health')
    def api_health():
        return jsonify({
            'status': 'healthy',
            'message': 'API is working',
            'features': ['basic-auth', 'health-check']
        })
    
    @app.route('/')
    def index():
        return jsonify({
            'message': 'Welcome to UniDynamics API',
            'endpoints': [
                '/health',
                '/api/health'
            ]
        })
    
    return app

def main():
    """Main startup function."""
    print("🚀 Starting UniDynamics Backend (Minimal Mode)")
    print("=" * 50)
    
    try:
        app = create_minimal_app()
        
        print("✅ Minimal backend started successfully")
        print("🌐 Backend API: http://localhost:5000")
        print("📚 Health check: http://localhost:5000/health")
        print("🛑 Press Ctrl+C to stop")
        print()
        print("Note: This is minimal mode. For full features, install all dependencies.")
        print()
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True
        )
        
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
