import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Checkbox,
  IconButton,
  Chip,
  Button,
  Divider,
  Collapse,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  FilterList as FilterIcon,
  Sort as SortIcon
} from '@mui/icons-material';

const SegmentSelector = ({
  segments = [],
  selectedSegments = [],
  onSegmentToggle,
  onSegmentEdit,
  onSegmentDelete,
  onSeek,
  onSelectAll,
  onSelectNone,
  onSelectByImportance
}) => {
  const [expandedSegment, setExpandedSegment] = useState(null);
  const [sortBy, setSortBy] = useState('time'); // time, importance, duration
  const [filterBy, setFilterBy] = useState('all'); // all, selected, unselected, high-importance
  const [searchTerm, setSearchTerm] = useState('');

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (startTime, endTime) => {
    const duration = endTime - startTime;
    return `${duration.toFixed(1)}s`;
  };

  const getImportanceColor = (score) => {
    if (score >= 0.8) return 'error';
    if (score >= 0.6) return 'warning';
    if (score >= 0.4) return 'info';
    return 'default';
  };

  const getImportanceLabel = (score) => {
    if (score >= 0.8) return 'High';
    if (score >= 0.6) return 'Medium';
    if (score >= 0.4) return 'Low';
    return 'Very Low';
  };

  // Filter segments
  const filteredSegments = React.useMemo(() => {
    let filtered = segments;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(segment =>
        (segment.title && segment.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (segment.summary && segment.summary.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (segment.topics && segment.topics.some(topic => 
          topic.toLowerCase().includes(searchTerm.toLowerCase())
        ))
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'selected':
        filtered = filtered.filter(segment =>
          selectedSegments.some(s => s.id === segment.id)
        );
        break;
      case 'unselected':
        filtered = filtered.filter(segment =>
          !selectedSegments.some(s => s.id === segment.id)
        );
        break;
      case 'high-importance':
        filtered = filtered.filter(segment => segment.importance_score >= 0.6);
        break;
      default:
        break;
    }

    return filtered;
  }, [segments, selectedSegments, searchTerm, filterBy]);

  // Sort segments
  const sortedSegments = React.useMemo(() => {
    const sorted = [...filteredSegments];
    
    switch (sortBy) {
      case 'importance':
        return sorted.sort((a, b) => b.importance_score - a.importance_score);
      case 'duration':
        return sorted.sort((a, b) => 
          (b.end_time - b.start_time) - (a.end_time - a.start_time)
        );
      case 'time':
      default:
        return sorted.sort((a, b) => a.start_time - b.start_time);
    }
  }, [filteredSegments, sortBy]);

  const handleSegmentToggle = (segment) => {
    if (onSegmentToggle) {
      onSegmentToggle(segment);
    }
  };

  const handleSegmentPlay = (segment) => {
    if (onSeek) {
      onSeek(segment.start_time);
    }
  };

  const handleExpandToggle = (segmentId) => {
    setExpandedSegment(expandedSegment === segmentId ? null : segmentId);
  };

  const selectedCount = selectedSegments.length;
  const totalDuration = selectedSegments.reduce(
    (sum, segment) => sum + (segment.end_time - segment.start_time), 
    0
  );

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" component="h3" gutterBottom>
          Video Segments
        </Typography>
        
        {/* Summary */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Chip 
            label={`${selectedCount} selected`} 
            color="primary" 
            size="small" 
          />
          <Chip 
            label={`${formatDuration(0, totalDuration)} total`} 
            variant="outlined" 
            size="small" 
          />
          <Chip 
            label={`${segments.length} segments`} 
            variant="outlined" 
            size="small" 
          />
        </Box>

        {/* Controls */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          <Button size="small" onClick={onSelectAll}>
            Select All
          </Button>
          <Button size="small" onClick={onSelectNone}>
            Select None
          </Button>
          <Button 
            size="small" 
            onClick={() => onSelectByImportance && onSelectByImportance(0.6)}
          >
            Select High Importance
          </Button>
        </Box>

        {/* Search and Filters */}
        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
          <TextField
            size="small"
            placeholder="Search segments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ flex: 1 }}
          />
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <InputLabel>Filter</InputLabel>
            <Select
              value={filterBy}
              label="Filter"
              onChange={(e) => setFilterBy(e.target.value)}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="selected">Selected</MenuItem>
              <MenuItem value="unselected">Unselected</MenuItem>
              <MenuItem value="high-importance">High Importance</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <InputLabel>Sort</InputLabel>
            <Select
              value={sortBy}
              label="Sort"
              onChange={(e) => setSortBy(e.target.value)}
            >
              <MenuItem value="time">Time</MenuItem>
              <MenuItem value="importance">Importance</MenuItem>
              <MenuItem value="duration">Duration</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Segments List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {sortedSegments.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {searchTerm || filterBy !== 'all' ? 'No segments match your criteria' : 'No segments available'}
            </Typography>
          </Box>
        ) : (
          <List dense>
            {sortedSegments.map((segment, index) => {
              const isSelected = selectedSegments.some(s => s.id === segment.id);
              const isExpanded = expandedSegment === segment.id;
              
              return (
                <React.Fragment key={segment.id || index}>
                  <ListItem
                    sx={{
                      bgcolor: isSelected ? 'action.selected' : 'transparent',
                      '&:hover': { bgcolor: 'action.hover' }
                    }}
                  >
                    <ListItemIcon>
                      <Checkbox
                        checked={isSelected}
                        onChange={() => handleSegmentToggle(segment)}
                        color="primary"
                      />
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2">
                            {segment.title || `Segment ${index + 1}`}
                          </Typography>
                          <Chip
                            label={getImportanceLabel(segment.importance_score)}
                            color={getImportanceColor(segment.importance_score)}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {formatTime(segment.start_time)} - {formatTime(segment.end_time)} 
                            ({formatDuration(segment.start_time, segment.end_time)})
                          </Typography>
                          {segment.summary && (
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                mt: 0.5,
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {segment.summary}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Tooltip title="Play from start">
                          <IconButton 
                            size="small" 
                            onClick={() => handleSegmentPlay(segment)}
                          >
                            <PlayIcon />
                          </IconButton>
                        </Tooltip>
                        
                        {segment.summary && (
                          <IconButton 
                            size="small"
                            onClick={() => handleExpandToggle(segment.id)}
                          >
                            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                          </IconButton>
                        )}
                        
                        {onSegmentEdit && (
                          <IconButton 
                            size="small" 
                            onClick={() => onSegmentEdit(segment)}
                          >
                            <EditIcon />
                          </IconButton>
                        )}
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  {/* Expanded Details */}
                  {segment.summary && (
                    <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                      <Box sx={{ pl: 4, pr: 2, pb: 2 }}>
                        <Typography variant="body2" paragraph>
                          {segment.summary}
                        </Typography>
                        
                        {segment.topics && segment.topics.length > 0 && (
                          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                            {segment.topics.map((topic, topicIndex) => (
                              <Chip
                                key={topicIndex}
                                label={topic}
                                size="small"
                                variant="outlined"
                                color="secondary"
                              />
                            ))}
                          </Box>
                        )}
                      </Box>
                    </Collapse>
                  )}
                  
                  {index < sortedSegments.length - 1 && <Divider />}
                </React.Fragment>
              );
            })}
          </List>
        )}
      </Box>
    </Paper>
  );
};

export default SegmentSelector;
