import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  CircularProgress,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  StepContent,
  Chip,
  Link,
  Divider
} from '@mui/material';
import {
  YouTube as YouTubeIcon,
  Upload as UploadIcon,
  Link as LinkIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import api from '../../services/api';

const YouTubeUploader = ({ project, onProjectUpdate }) => {
  const [uploadData, setUploadData] = useState({
    title: '',
    description: '',
    tags: [],
    privacy: 'unlisted'
  });
  const [authStatus, setAuthStatus] = useState('unknown'); // unknown, authorized, unauthorized
  const [uploadStatus, setUploadStatus] = useState('idle'); // idle, uploading, completed, failed
  const [uploadProgress, setUploadProgress] = useState(0);
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [activeStep, setActiveStep] = useState(0);

  useEffect(() => {
    if (project) {
      setUploadData({
        title: project.ai_title || project.title || '',
        description: project.ai_description || project.description || '',
        tags: project.ai_tags || [],
        privacy: 'unlisted'
      });
      
      if (project.youtube_url) {
        setYoutubeUrl(project.youtube_url);
        setUploadStatus('completed');
        setActiveStep(3);
      }
    }
    
    checkAuthStatus();
  }, [project]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const checkAuthStatus = async () => {
    try {
      // Check if user has YouTube credentials
      // This would be implemented based on your auth system
      setAuthStatus('unauthorized'); // Default for now
    } catch (error) {
      setAuthStatus('unauthorized');
    }
  };

  const handleYouTubeAuth = async () => {
    try {
      const response = await api.get('/advanced/youtube/auth');
      
      if (response.data.auth_url) {
        // Open YouTube OAuth in new window
        window.open(response.data.auth_url, 'youtube-auth', 'width=600,height=600');
        
        // Listen for auth completion
        const checkAuth = setInterval(() => {
          // In a real implementation, you'd check if auth completed
          // For now, we'll simulate success after a delay
          setTimeout(() => {
            setAuthStatus('authorized');
            setActiveStep(1);
            clearInterval(checkAuth);
            showSnackbar('YouTube authorization successful!', 'success');
          }, 5000);
        }, 1000);
      }
    } catch (error) {
      showSnackbar(error.response?.data?.error || 'Failed to get YouTube auth URL', 'error');
    }
  };

  const handleUpload = async () => {
    try {
      setUploadStatus('uploading');
      setActiveStep(2);
      
      const response = await api.post(`/advanced/projects/${project.id}/youtube-upload`, uploadData);
      
      showSnackbar('YouTube upload started! This may take several minutes.', 'info');
      
      // Poll for upload completion
      pollUploadStatus(response.data.task_id);
      
    } catch (error) {
      setUploadStatus('failed');
      showSnackbar(error.response?.data?.error || 'Failed to start YouTube upload', 'error');
    }
  };

  const pollUploadStatus = async (taskId) => {
    const pollInterval = setInterval(async () => {
      try {
        // Simulate upload progress
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
        
        // Check if project has YouTube URL (upload completed)
        const projectResponse = await api.get(`/projects/${project.id}`);
        
        if (projectResponse.data.project.youtube_url) {
          setYoutubeUrl(projectResponse.data.project.youtube_url);
          setUploadStatus('completed');
          setUploadProgress(100);
          setActiveStep(3);
          
          if (onProjectUpdate) {
            onProjectUpdate(projectResponse.data.project);
          }
          
          clearInterval(pollInterval);
          showSnackbar('Video uploaded to YouTube successfully!', 'success');
        }
        
      } catch (error) {
        console.log('Still uploading...');
      }
    }, 3000);
    
    // Stop polling after 30 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      if (uploadStatus === 'uploading') {
        setUploadStatus('failed');
        showSnackbar('Upload timeout. Please check YouTube directly.', 'warning');
      }
    }, 1800000);
  };

  const handlePrivacyChange = async (videoId, newPrivacy) => {
    try {
      await api.put(`/advanced/youtube/videos/${videoId}/privacy`, {
        privacy_status: newPrivacy
      });
      
      showSnackbar(`Video privacy updated to ${newPrivacy}`, 'success');
    } catch (error) {
      showSnackbar('Failed to update video privacy', 'error');
    }
  };

  const getStepIcon = (step) => {
    switch (step) {
      case 0:
        return authStatus === 'authorized' ? <CheckCircleIcon color="success" /> : <YouTubeIcon />;
      case 1:
        return <UploadIcon />;
      case 2:
        return uploadStatus === 'uploading' ? <CircularProgress size={24} /> : 
               uploadStatus === 'completed' ? <CheckCircleIcon color="success" /> :
               uploadStatus === 'failed' ? <ErrorIcon color="error" /> : <UploadIcon />;
      case 3:
        return <LinkIcon />;
      default:
        return null;
    }
  };

  const steps = [
    {
      label: 'Authorize YouTube',
      description: 'Connect your YouTube account to upload videos',
      completed: authStatus === 'authorized'
    },
    {
      label: 'Configure Upload',
      description: 'Set title, description, and privacy settings',
      completed: false
    },
    {
      label: 'Upload Video',
      description: 'Upload your video to YouTube',
      completed: uploadStatus === 'completed'
    },
    {
      label: 'Manage Video',
      description: 'View and manage your uploaded video',
      completed: uploadStatus === 'completed'
    }
  ];

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
        <YouTubeIcon color="error" />
        <Typography variant="h6" component="h3">
          YouTube Upload
        </Typography>
        {uploadStatus === 'completed' && (
          <Chip label="Uploaded" color="success" size="small" />
        )}
      </Box>

      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((step, index) => (
          <Step key={index} completed={step.completed}>
            <StepLabel icon={getStepIcon(index)}>
              {step.label}
            </StepLabel>
            <StepContent>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {step.description}
              </Typography>

              {/* Step 0: Authorization */}
              {index === 0 && authStatus !== 'authorized' && (
                <Button
                  variant="contained"
                  startIcon={<YouTubeIcon />}
                  onClick={handleYouTubeAuth}
                  sx={{ bgcolor: '#FF0000', '&:hover': { bgcolor: '#CC0000' } }}
                >
                  Connect YouTube Account
                </Button>
              )}

              {index === 0 && authStatus === 'authorized' && (
                <Alert severity="success">
                  YouTube account connected successfully!
                </Alert>
              )}

              {/* Step 1: Configure Upload */}
              {index === 1 && authStatus === 'authorized' && (
                <Box sx={{ maxWidth: 500 }}>
                  <TextField
                    fullWidth
                    label="Video Title"
                    value={uploadData.title}
                    onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
                    sx={{ mb: 2 }}
                    helperText={`${uploadData.title.length}/100 characters`}
                    inputProps={{ maxLength: 100 }}
                  />
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Video Description"
                    value={uploadData.description}
                    onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                    sx={{ mb: 2 }}
                  />
                  
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Privacy Setting</InputLabel>
                    <Select
                      value={uploadData.privacy}
                      label="Privacy Setting"
                      onChange={(e) => setUploadData(prev => ({ ...prev, privacy: e.target.value }))}
                    >
                      <MenuItem value="private">Private</MenuItem>
                      <MenuItem value="unlisted">Unlisted</MenuItem>
                      <MenuItem value="public">Public</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    {uploadData.tags.map((tag, tagIndex) => (
                      <Chip
                        key={tagIndex}
                        label={tag}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                  
                  <Button
                    variant="contained"
                    startIcon={<UploadIcon />}
                    onClick={handleUpload}
                    disabled={!uploadData.title || uploadStatus === 'uploading'}
                  >
                    Start Upload
                  </Button>
                </Box>
              )}

              {/* Step 2: Upload Progress */}
              {index === 2 && uploadStatus === 'uploading' && (
                <Box sx={{ width: '100%', maxWidth: 400 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Uploading to YouTube... {Math.round(uploadProgress)}%
                  </Typography>
                  <Box sx={{ width: '100%', bgcolor: 'grey.200', borderRadius: 1, height: 8 }}>
                    <Box
                      sx={{
                        width: `${uploadProgress}%`,
                        bgcolor: 'primary.main',
                        height: '100%',
                        borderRadius: 1,
                        transition: 'width 0.3s ease'
                      }}
                    />
                  </Box>
                </Box>
              )}

              {index === 2 && uploadStatus === 'completed' && (
                <Alert severity="success">
                  Video uploaded successfully!
                </Alert>
              )}

              {index === 2 && uploadStatus === 'failed' && (
                <Alert severity="error">
                  Upload failed. Please try again.
                </Alert>
              )}

              {/* Step 3: Manage Video */}
              {index === 3 && youtubeUrl && (
                <Box>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    Your video is now available on YouTube:
                  </Typography>
                  
                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                    <Link
                      href={youtubeUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                    >
                      <YouTubeIcon color="error" />
                      View on YouTube
                    </Link>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary">
                    You can change the privacy setting and other details directly on YouTube.
                  </Typography>
                </Box>
              )}
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default YouTubeUploader;
