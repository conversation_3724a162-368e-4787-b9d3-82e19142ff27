# UniDynamics Local Development Setup Script for Windows PowerShell
# This script guides you through setting up UniDynamics for local development

Write-Host "UniDynamics Local Development Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Helper functions
function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Check system requirements
Write-Host "Checking system requirements..." -ForegroundColor Cyan
Write-Host ""

# Check Python
if (Test-Command "python") {
    $pythonVersion = python --version
    Write-Success "Python found: $pythonVersion"
} else {
    Write-Error "Python 3.9+ is required"
    Write-Host "Install from: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    exit 1
}

# Check Node.js
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-Success "Node.js found: $nodeVersion"
} else {
    Write-Error "Node.js 16+ is required"
    Write-Host "Install from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check PostgreSQL
if (Test-Command "psql") {
    Write-Success "PostgreSQL found"
} else {
    Write-Warning "PostgreSQL not found in PATH"
    Write-Host "Install PostgreSQL 13+ from: https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    Write-Host "Make sure to add PostgreSQL bin directory to PATH" -ForegroundColor Yellow
}

# Check Redis
if (Test-Command "redis-server") {
    Write-Success "Redis found"
} else {
    Write-Warning "Redis not found"
    Write-Host "Install Redis:" -ForegroundColor Yellow
    Write-Host "  Option 1: Use WSL2 - wsl --install, then sudo apt-get install redis-server" -ForegroundColor Yellow
    Write-Host "  Option 2: Download Windows port from GitHub" -ForegroundColor Yellow
}

# Check FFmpeg
if (Test-Command "ffmpeg") {
    Write-Success "FFmpeg found"
} else {
    Write-Warning "FFmpeg not found"
    Write-Host "Install FFmpeg:" -ForegroundColor Yellow
    Write-Host "  1. Download from https://ffmpeg.org/download.html#build-windows" -ForegroundColor Yellow
    Write-Host "  2. Extract to a folder (e.g., C:\ffmpeg)" -ForegroundColor Yellow
    Write-Host "  3. Add C:\ffmpeg\bin to your PATH environment variable" -ForegroundColor Yellow
}

# Check Ollama
if (Test-Command "ollama") {
    Write-Success "Ollama found"
} else {
    Write-Warning "Ollama not found"
    Write-Host "Install Ollama from: https://ollama.ai/download" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setting up project structure..." -ForegroundColor Cyan

# Create necessary directories
$directories = @(
    "backend\uploads",
    "backend\logs",
    "frontend\build",
    "docs\api",
    "tests\backend",
    "tests\frontend"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Success "Project directories created"

Write-Host ""
Write-Host "Setting up environment files..." -ForegroundColor Cyan

# Backend environment
if (!(Test-Path "backend\.env")) {
    if (Test-Path "backend\.env.local") {
        Copy-Item "backend\.env.local" "backend\.env"
        Write-Success "Backend .env file created from .env.local"
        Write-Info "Please edit backend\.env with your local database credentials"
    } else {
        Copy-Item "backend\.env.example" "backend\.env"
        Write-Success "Backend .env file created from .env.example"
        Write-Info "Please edit backend\.env with your configuration"
    }
} else {
    Write-Info "Backend .env file already exists"
}

# Frontend environment
if (!(Test-Path "frontend\.env")) {
    Copy-Item "frontend\.env.example" "frontend\.env"
    Write-Success "Frontend .env file created"
} else {
    Write-Info "Frontend .env file already exists"
}

Write-Host ""
Write-Host "Setting up Python environment..." -ForegroundColor Cyan

Set-Location backend

# Create virtual environment
if (!(Test-Path "venv")) {
    python -m venv venv
    Write-Success "Python virtual environment created"
} else {
    Write-Info "Virtual environment already exists"
}

# Activate virtual environment and install dependencies
Write-Info "Installing Python dependencies..."
& "venv\Scripts\activate.ps1"
python -m pip install --upgrade pip

# Try minimal requirements first
if (Test-Path "requirements-minimal.txt") {
    pip install -r requirements-minimal.txt
} else {
    pip install -r requirements.txt
}

Write-Success "Python dependencies installed"

Set-Location ..

Write-Host ""
Write-Host "Setting up Node.js environment..." -ForegroundColor Cyan

Set-Location frontend

# Install dependencies
Write-Info "Installing Node.js dependencies..."
npm install

Write-Success "Node.js dependencies installed"

Set-Location ..

Write-Host ""
Write-Host "Database setup..." -ForegroundColor Cyan

Write-Info "Please ensure PostgreSQL is running and create the database:"
Write-Host ""
Write-Host "1. Open Command Prompt or PowerShell as Administrator" -ForegroundColor White
Write-Host "2. Connect to PostgreSQL: psql -U postgres" -ForegroundColor White
Write-Host "3. Create database: CREATE DATABASE unidynamics;" -ForegroundColor White
Write-Host "4. Create user: CREATE USER unidynamics WITH PASSWORD 'unidynamics_dev';" -ForegroundColor White
Write-Host "5. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE unidynamics TO unidynamics;" -ForegroundColor White
Write-Host ""

$dbCreated = Read-Host "Have you created the database? (y/n)"

if ($dbCreated -eq "y" -or $dbCreated -eq "Y") {
    Set-Location backend
    & "venv\Scripts\activate.ps1"
    
    Write-Info "Initializing database..."
    python run.py init-db
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Database initialized successfully"
    } else {
        Write-Error "Database initialization failed"
        Write-Info "Please check your database connection settings in backend\.env"
    }
    
    Set-Location ..
} else {
    Write-Warning "Please create the database manually and run: python run.py init-db"
}

Write-Host ""
Write-Host "Ollama setup..." -ForegroundColor Cyan

if (Test-Command "ollama") {
    Write-Info "Please start Ollama in a separate terminal: ollama serve"
    Write-Info "Then download models: ollama pull llama2"
    Write-Host ""
    Write-Host "You can do this now or later." -ForegroundColor Yellow
} else {
    Write-Warning "Ollama not found. Please install from https://ollama.ai/download"
}

Write-Host ""
Write-Host "Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "To start development:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Start required services:" -ForegroundColor White
Write-Host "   - PostgreSQL (usually auto-starts)" -ForegroundColor Gray
Write-Host "   - Redis: redis-server (or in WSL)" -ForegroundColor Gray
Write-Host "   - Ollama: ollama serve" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Start application services:" -ForegroundColor White
Write-Host "   Terminal 1 - Backend API:" -ForegroundColor Gray
Write-Host "   cd backend; .\venv\Scripts\activate.ps1; python run.py" -ForegroundColor Gray
Write-Host ""
Write-Host "   Terminal 2 - Celery Worker:" -ForegroundColor Gray
Write-Host "   cd backend; .\venv\Scripts\activate.ps1; celery -A app.celery worker --loglevel=info" -ForegroundColor Gray
Write-Host ""
Write-Host "   Terminal 3 - Frontend:" -ForegroundColor Gray
Write-Host "   cd frontend; npm start" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Access the application:" -ForegroundColor White
Write-Host "   - Frontend: http://localhost:3000" -ForegroundColor Gray
Write-Host "   - Backend API: http://localhost:5000" -ForegroundColor Gray
Write-Host ""
Write-Host "Documentation:" -ForegroundColor Cyan
Write-Host "   - Local Setup Guide: docs\LOCAL_SETUP.md" -ForegroundColor White
Write-Host "   - Development Guide: docs\DEVELOPMENT.md" -ForegroundColor White
Write-Host "   - API Documentation: docs\API.md" -ForegroundColor White
Write-Host ""
Write-Host "Need help?" -ForegroundColor Cyan
Write-Host "   - Check the troubleshooting section in docs\LOCAL_SETUP.md" -ForegroundColor White
Write-Host "   - Verify all services are running" -ForegroundColor White
Write-Host "   - Check logs for error messages" -ForegroundColor White
Write-Host ""
Write-Success "Happy coding!"
