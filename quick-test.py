#!/usr/bin/env python3
"""
UniDynamics Quick Test Script
Tests all major functionality to ensure the platform is working correctly.
"""

import requests
import time
import json
import sys
import os
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:5000/api"
FRONTEND_URL = "http://localhost:3000"

# Test data
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "TestPassword123!"
}

TEST_PROJECT = {
    "title": "Test Video Summary Project",
    "description": "Automated test project for UniDynamics"
}

TEST_VIDEO_URL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Short test video

class UniDynamicsTest:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.user_id = None
        self.project_id = None
        self.video_id = None
        
    def log(self, message, status="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_colors = {
            "INFO": "\033[94m",    # Blue
            "SUCCESS": "\033[92m", # Green
            "ERROR": "\033[91m",   # Red
            "WARNING": "\033[93m"  # Yellow
        }
        reset_color = "\033[0m"
        color = status_colors.get(status, "")
        print(f"{color}[{timestamp}] {status}: {message}{reset_color}")
    
    def test_service_health(self):
        """Test if all services are running"""
        self.log("Testing service health...", "INFO")
        
        # Test backend API
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                self.log("✅ Backend API is healthy", "SUCCESS")
            else:
                self.log(f"❌ Backend API returned status {response.status_code}", "ERROR")
                return False
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Backend API is not responding: {e}", "ERROR")
            return False
        
        # Test frontend
        try:
            response = requests.get(FRONTEND_URL, timeout=5)
            if response.status_code == 200:
                self.log("✅ Frontend is responding", "SUCCESS")
            else:
                self.log(f"❌ Frontend returned status {response.status_code}", "ERROR")
        except requests.exceptions.RequestException as e:
            self.log(f"⚠️  Frontend is not responding: {e}", "WARNING")
        
        return True
    
    def test_user_registration(self):
        """Test user registration"""
        self.log("Testing user registration...", "INFO")
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/auth/register",
                json=TEST_USER,
                timeout=10
            )
            
            if response.status_code == 201:
                self.log("✅ User registration successful", "SUCCESS")
                return True
            elif response.status_code == 400 and "already exists" in response.text:
                self.log("⚠️  User already exists, proceeding with login", "WARNING")
                return True
            else:
                self.log(f"❌ Registration failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Registration request failed: {e}", "ERROR")
            return False
    
    def test_user_login(self):
        """Test user login"""
        self.log("Testing user login...", "INFO")
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/auth/login",
                json={
                    "username": TEST_USER["username"],
                    "password": TEST_USER["password"]
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                self.user_id = data.get("user", {}).get("id")
                
                # Set authorization header for future requests
                self.session.headers.update({
                    "Authorization": f"Bearer {self.auth_token}"
                })
                
                self.log("✅ User login successful", "SUCCESS")
                return True
            else:
                self.log(f"❌ Login failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Login request failed: {e}", "ERROR")
            return False
    
    def test_project_creation(self):
        """Test project creation"""
        self.log("Testing project creation...", "INFO")
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/projects",
                json=TEST_PROJECT,
                timeout=10
            )
            
            if response.status_code == 201:
                data = response.json()
                self.project_id = data.get("project", {}).get("id")
                self.log(f"✅ Project created successfully (ID: {self.project_id})", "SUCCESS")
                return True
            else:
                self.log(f"❌ Project creation failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Project creation request failed: {e}", "ERROR")
            return False
    
    def test_video_addition(self):
        """Test adding a video to project"""
        self.log("Testing video addition...", "INFO")
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/videos",
                json={
                    "project_id": self.project_id,
                    "youtube_url": TEST_VIDEO_URL
                },
                timeout=15
            )
            
            if response.status_code == 201:
                data = response.json()
                self.video_id = data.get("video", {}).get("id")
                self.log(f"✅ Video added successfully (ID: {self.video_id})", "SUCCESS")
                return True
            else:
                self.log(f"❌ Video addition failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Video addition request failed: {e}", "ERROR")
            return False
    
    def test_video_processing(self):
        """Test video processing pipeline"""
        self.log("Testing video processing pipeline...", "INFO")
        
        if not self.video_id:
            self.log("❌ No video ID available for processing test", "ERROR")
            return False
        
        # Poll for processing completion
        max_wait_time = 300  # 5 minutes
        poll_interval = 10   # 10 seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                response = self.session.get(
                    f"{API_BASE_URL}/videos/{self.video_id}/status",
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status", "unknown")
                    
                    self.log(f"Processing status: {status}", "INFO")
                    
                    if status == "completed":
                        self.log("✅ Video processing completed successfully", "SUCCESS")
                        return True
                    elif status == "failed":
                        error_msg = data.get("error_message", "Unknown error")
                        self.log(f"❌ Video processing failed: {error_msg}", "ERROR")
                        return False
                    
                    # Continue polling
                    time.sleep(poll_interval)
                else:
                    self.log(f"❌ Status check failed: {response.text}", "ERROR")
                    return False
                    
            except requests.exceptions.RequestException as e:
                self.log(f"❌ Status check request failed: {e}", "ERROR")
                return False
        
        self.log("⚠️  Video processing timeout (may still be processing)", "WARNING")
        return False
    
    def test_segments_retrieval(self):
        """Test retrieving video segments"""
        self.log("Testing segments retrieval...", "INFO")
        
        try:
            response = self.session.get(
                f"{API_BASE_URL}/videos/{self.video_id}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                video = data.get("video", {})
                segments = video.get("segments", [])
                
                if segments:
                    self.log(f"✅ Retrieved {len(segments)} segments", "SUCCESS")
                    return True
                else:
                    self.log("⚠️  No segments found (may still be processing)", "WARNING")
                    return False
            else:
                self.log(f"❌ Segments retrieval failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Segments retrieval request failed: {e}", "ERROR")
            return False
    
    def test_ai_features(self):
        """Test AI metadata generation"""
        self.log("Testing AI features...", "INFO")
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/advanced/projects/{self.project_id}/ai-metadata",
                json={"options": ["title", "description", "tags"]},
                timeout=15
            )
            
            if response.status_code == 200:
                self.log("✅ AI metadata generation started", "SUCCESS")
                
                # Wait a bit and check results
                time.sleep(30)
                
                response = self.session.get(
                    f"{API_BASE_URL}/advanced/projects/{self.project_id}/ai-metadata",
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("ai_title") or data.get("ai_description") or data.get("ai_tags"):
                        self.log("✅ AI metadata generated successfully", "SUCCESS")
                        return True
                    else:
                        self.log("⚠️  AI metadata generation in progress", "WARNING")
                        return False
                        
            else:
                self.log(f"❌ AI metadata generation failed: {response.text}", "ERROR")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log(f"❌ AI features request failed: {e}", "ERROR")
            return False
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        self.log("🚀 Starting UniDynamics comprehensive test suite", "INFO")
        self.log("=" * 60, "INFO")
        
        tests = [
            ("Service Health", self.test_service_health),
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("Project Creation", self.test_project_creation),
            ("Video Addition", self.test_video_addition),
            ("Video Processing", self.test_video_processing),
            ("Segments Retrieval", self.test_segments_retrieval),
            ("AI Features", self.test_ai_features),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            self.log(f"\n🧪 Running test: {test_name}", "INFO")
            self.log("-" * 40, "INFO")
            
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                self.log(f"❌ Test {test_name} crashed: {e}", "ERROR")
                failed += 1
        
        # Summary
        self.log("\n" + "=" * 60, "INFO")
        self.log("🎯 TEST SUMMARY", "INFO")
        self.log("=" * 60, "INFO")
        self.log(f"✅ Passed: {passed}", "SUCCESS")
        self.log(f"❌ Failed: {failed}", "ERROR" if failed > 0 else "INFO")
        self.log(f"📊 Total:  {passed + failed}", "INFO")
        
        if failed == 0:
            self.log("\n🎉 ALL TESTS PASSED! UniDynamics is working correctly.", "SUCCESS")
            return True
        else:
            self.log(f"\n⚠️  {failed} test(s) failed. Check the logs above for details.", "WARNING")
            return False

def main():
    """Main test execution"""
    print("🧪 UniDynamics Quick Test Script")
    print("=" * 40)
    print("This script will test all major functionality of UniDynamics.")
    print("Make sure all services are running before proceeding.\n")
    
    # Check if services are likely running
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Backend API is not responding. Please start the backend services first.")
            print("Run: start-backend.bat")
            sys.exit(1)
    except:
        print("❌ Backend API is not responding. Please start the backend services first.")
        print("Run: start-backend.bat")
        sys.exit(1)
    
    # Run tests
    tester = UniDynamicsTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 UniDynamics is ready for use!")
        print(f"🌐 Access the application at: {FRONTEND_URL}")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please check the setup and try again.")
        print("See SETUP_AND_TEST_GUIDE.md for troubleshooting.")
        sys.exit(1)

if __name__ == "__main__":
    main()
