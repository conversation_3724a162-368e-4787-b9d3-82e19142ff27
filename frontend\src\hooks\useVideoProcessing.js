import { useState, useEffect, useCallback } from 'react';
import api from '../services/api';

const useVideoProcessing = (projectId) => {
  const [project, setProject] = useState(null);
  const [videos, setVideos] = useState([]);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [segments, setSegments] = useState([]);
  const [selectedSegments, setSelectedSegments] = useState([]);
  const [transcript, setTranscript] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [processingStatus, setProcessingStatus] = useState({});

  // Fetch project data
  const fetchProject = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/projects/${projectId}`);
      setProject(response.data.project);
      
      // Fetch project videos
      const videosResponse = await api.get(`/projects/${projectId}/videos`);
      setVideos(videosResponse.data.videos);
      
      // Set first video as selected if available
      if (videosResponse.data.videos.length > 0) {
        setSelectedVideo(videosResponse.data.videos[0]);
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch project');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // Fetch video details including segments and transcript
  const fetchVideoDetails = useCallback(async (videoId) => {
    if (!videoId) return;
    
    try {
      const response = await api.get(`/videos/${videoId}`);
      const videoData = response.data.video;
      
      setTranscript(videoData.transcript);
      setSegments(videoData.segments || []);
      
      // Set initially selected segments (AI suggested ones)
      const aiSuggested = videoData.segments?.filter(s => s.is_ai_suggested) || [];
      setSelectedSegments(aiSuggested);
      
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch video details');
    }
  }, []);

  // Add video to project
  const addVideo = useCallback(async (youtubeUrl) => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      const response = await api.post('/videos', {
        project_id: projectId,
        youtube_url: youtubeUrl
      });
      
      const newVideo = response.data.video;
      setVideos(prev => [...prev, newVideo]);
      
      // Set as selected video if it's the first one
      if (videos.length === 0) {
        setSelectedVideo(newVideo);
      }
      
      // Start polling for processing status
      pollVideoStatus(newVideo.id);
      
      return newVideo;
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to add video');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [projectId, videos.length]);

  // Poll video processing status
  const pollVideoStatus = useCallback(async (videoId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await api.get(`/videos/${videoId}/status`);
        const status = response.data;
        
        setProcessingStatus(prev => ({
          ...prev,
          [videoId]: status
        }));
        
        // If processing is complete, fetch full video details
        if (status.status === 'completed') {
          clearInterval(pollInterval);
          await fetchVideoDetails(videoId);
          
          // Update video in list
          setVideos(prev => prev.map(v => 
            v.id === videoId 
              ? { ...v, status: status.status }
              : v
          ));
        } else if (status.status === 'failed') {
          clearInterval(pollInterval);
          setError(status.error_message || 'Video processing failed');
        }
      } catch (err) {
        console.error('Error polling video status:', err);
        clearInterval(pollInterval);
      }
    }, 2000);
    
    // Cleanup after 10 minutes
    setTimeout(() => clearInterval(pollInterval), 600000);
  }, [fetchVideoDetails]);

  // Toggle segment selection
  const toggleSegment = useCallback((segment) => {
    setSelectedSegments(prev => {
      const isSelected = prev.some(s => s.id === segment.id);
      if (isSelected) {
        return prev.filter(s => s.id !== segment.id);
      } else {
        return [...prev, segment];
      }
    });
  }, []);

  // Select all segments
  const selectAllSegments = useCallback(() => {
    setSelectedSegments([...segments]);
  }, [segments]);

  // Select no segments
  const selectNoSegments = useCallback(() => {
    setSelectedSegments([]);
  }, []);

  // Select segments by importance threshold
  const selectSegmentsByImportance = useCallback((threshold = 0.6) => {
    const importantSegments = segments.filter(s => s.importance_score >= threshold);
    setSelectedSegments(importantSegments);
  }, [segments]);

  // Update segment selection on backend
  const updateSegmentSelection = useCallback(async () => {
    if (!selectedVideo) return;
    
    try {
      const updates = segments.map(segment => ({
        id: segment.id,
        is_selected: selectedSegments.some(s => s.id === segment.id)
      }));
      
      await api.put(`/videos/${selectedVideo.id}/segments`, {
        segments: updates
      });
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to update segment selection');
    }
  }, [selectedVideo, segments, selectedSegments]);

  // Regenerate AI segments
  const regenerateSegments = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      await api.post(`/processing/project/${projectId}/regenerate-segments`);
      
      // Refresh video details after regeneration
      if (selectedVideo) {
        await fetchVideoDetails(selectedVideo.id);
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to regenerate segments');
    } finally {
      setLoading(false);
    }
  }, [projectId, selectedVideo, fetchVideoDetails]);

  // Start video rendering
  const startRendering = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      
      // First update segment selection
      await updateSegmentSelection();
      
      // Then start rendering
      const response = await api.post('/processing/render', {
        project_id: projectId
      });
      
      // Update project status
      setProject(prev => ({
        ...prev,
        status: 'processing'
      }));
      
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to start rendering');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [projectId, updateSegmentSelection]);

  // Get download URL
  const getDownloadUrl = useCallback(async () => {
    if (!projectId) return null;
    
    try {
      const response = await api.get(`/processing/project/${projectId}/download`);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to get download URL');
      return null;
    }
  }, [projectId]);

  // Initialize data when projectId changes
  useEffect(() => {
    if (projectId) {
      fetchProject();
    }
  }, [projectId, fetchProject]);

  // Fetch video details when selected video changes
  useEffect(() => {
    if (selectedVideo?.id) {
      fetchVideoDetails(selectedVideo.id);
    }
  }, [selectedVideo?.id, fetchVideoDetails]);

  // Auto-update segment selection on backend when it changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (selectedVideo && segments.length > 0) {
        updateSegmentSelection();
      }
    }, 1000); // Debounce updates
    
    return () => clearTimeout(timeoutId);
  }, [selectedSegments, updateSegmentSelection, selectedVideo, segments.length]);

  return {
    // Data
    project,
    videos,
    selectedVideo,
    segments,
    selectedSegments,
    transcript,
    processingStatus,
    
    // State
    loading,
    error,
    
    // Actions
    setSelectedVideo,
    addVideo,
    toggleSegment,
    selectAllSegments,
    selectNoSegments,
    selectSegmentsByImportance,
    regenerateSegments,
    startRendering,
    getDownloadUrl,
    fetchProject,
    setError
  };
};

export default useVideoProcessing;
