import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Alert,
  Snackbar,
  Grid,
  Slider,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Upload as UploadIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  VolumeUp as VolumeIcon
} from '@mui/icons-material';
import api from '../../services/api';

const ProjectSettings = ({ project, onProjectUpdate }) => {
  const [settings, setSettings] = useState({
    compilation_mode: 'single',
    transition_type: 'cut',
    transition_duration: 0.5,
    background_music_volume: 0.2,
    auto_chapters: false,
    youtube_upload_enabled: false,
    max_duration: 600,
    include_intro: false,
    include_outro: false
  });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [uploadDialog, setUploadDialog] = useState({ open: false, type: '' });

  useEffect(() => {
    if (project) {
      setSettings({
        compilation_mode: project.compilation_mode || 'single',
        transition_type: project.transition_type || 'cut',
        transition_duration: project.transition_duration || 0.5,
        background_music_volume: project.background_music_volume || 0.2,
        auto_chapters: project.auto_chapters || false,
        youtube_upload_enabled: project.youtube_upload_enabled || false,
        max_duration: project.max_duration || 600,
        include_intro: project.include_intro || false,
        include_outro: project.include_outro || false
      });
    }
  }, [project]);

  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSettingChange = (field, value) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      const response = await api.put(`/multi-video/projects/${project.id}/settings`, settings);
      
      if (onProjectUpdate) {
        onProjectUpdate(response.data.project);
      }
      
      showSnackbar('Settings saved successfully', 'success');
    } catch (error) {
      showSnackbar(error.response?.data?.error || 'Failed to save settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file, type) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);
      
      if (type === 'background-music') {
        formData.append('volume', settings.background_music_volume);
      }
      
      const endpoint = `/multi-video/projects/${project.id}/${type}`;
      const response = await api.post(endpoint, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      
      if (onProjectUpdate) {
        onProjectUpdate(response.data.project);
      }
      
      showSnackbar(`${type.replace('-', ' ')} uploaded successfully`, 'success');
      setUploadDialog({ open: false, type: '' });
    } catch (error) {
      showSnackbar(error.response?.data?.error || `Failed to upload ${type}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" component="h3" gutterBottom>
        Project Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Basic Settings */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Basic Settings
          </Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Compilation Mode</InputLabel>
            <Select
              value={settings.compilation_mode}
              label="Compilation Mode"
              onChange={(e) => handleSettingChange('compilation_mode', e.target.value)}
            >
              <MenuItem value="single">Single Video</MenuItem>
              <MenuItem value="multi">Multi-Video Compilation</MenuItem>
              <MenuItem value="timeline">Advanced Timeline</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Maximum Duration (seconds)"
            type="number"
            value={settings.max_duration}
            onChange={(e) => handleSettingChange('max_duration', parseInt(e.target.value))}
            inputProps={{ min: 60, max: 3600 }}
            sx={{ mb: 2 }}
            helperText={`Current: ${formatDuration(settings.max_duration)}`}
          />

          <FormControlLabel
            control={
              <Switch
                checked={settings.auto_chapters}
                onChange={(e) => handleSettingChange('auto_chapters', e.target.checked)}
              />
            }
            label="Auto-generate Chapters"
            sx={{ mb: 1 }}
          />

          <FormControlLabel
            control={
              <Switch
                checked={settings.youtube_upload_enabled}
                onChange={(e) => handleSettingChange('youtube_upload_enabled', e.target.checked)}
              />
            }
            label="Enable YouTube Upload"
          />
        </Grid>

        {/* Transition Settings */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Transition Settings
          </Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Default Transition Type</InputLabel>
            <Select
              value={settings.transition_type}
              label="Default Transition Type"
              onChange={(e) => handleSettingChange('transition_type', e.target.value)}
            >
              <MenuItem value="cut">Cut</MenuItem>
              <MenuItem value="fade">Fade</MenuItem>
              <MenuItem value="dissolve">Dissolve</MenuItem>
            </Select>
          </FormControl>

          {settings.transition_type !== 'cut' && (
            <Box sx={{ mb: 2 }}>
              <Typography gutterBottom>
                Transition Duration: {settings.transition_duration}s
              </Typography>
              <Slider
                value={settings.transition_duration}
                onChange={(e, value) => handleSettingChange('transition_duration', value)}
                min={0.1}
                max={3.0}
                step={0.1}
                marks={[
                  { value: 0.1, label: '0.1s' },
                  { value: 1.0, label: '1s' },
                  { value: 3.0, label: '3s' }
                ]}
              />
            </Box>
          )}
        </Grid>

        {/* Media Files */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Media Files
          </Typography>

          <Grid container spacing={2}>
            {/* Intro */}
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Intro Video
                </Typography>
                
                {project?.intro_file_url ? (
                  <Box>
                    <Chip 
                      label="Uploaded" 
                      color="success" 
                      size="small" 
                      sx={{ mb: 1 }}
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small" color="primary">
                        <PlayIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Box>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialog({ open: true, type: 'intro' })}
                    fullWidth
                  >
                    Upload Intro
                  </Button>
                )}
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.include_intro}
                      onChange={(e) => handleSettingChange('include_intro', e.target.checked)}
                    />
                  }
                  label="Include in video"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Grid>

            {/* Outro */}
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Outro Video
                </Typography>
                
                {project?.outro_file_url ? (
                  <Box>
                    <Chip 
                      label="Uploaded" 
                      color="success" 
                      size="small" 
                      sx={{ mb: 1 }}
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small" color="primary">
                        <PlayIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Box>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialog({ open: true, type: 'outro' })}
                    fullWidth
                  >
                    Upload Outro
                  </Button>
                )}
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.include_outro}
                      onChange={(e) => handleSettingChange('include_outro', e.target.checked)}
                    />
                  }
                  label="Include in video"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Grid>

            {/* Background Music */}
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Background Music
                </Typography>
                
                {project?.background_music_url ? (
                  <Box>
                    <Chip 
                      label="Uploaded" 
                      color="success" 
                      size="small" 
                      sx={{ mb: 1 }}
                    />
                    <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                      <IconButton size="small" color="primary">
                        <PlayIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <VolumeIcon fontSize="small" />
                      <Slider
                        value={settings.background_music_volume}
                        onChange={(e, value) => handleSettingChange('background_music_volume', value)}
                        min={0}
                        max={1}
                        step={0.1}
                        size="small"
                        sx={{ flex: 1 }}
                      />
                      <Typography variant="caption">
                        {Math.round(settings.background_music_volume * 100)}%
                      </Typography>
                    </Box>
                  </Box>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialog({ open: true, type: 'background-music' })}
                    fullWidth
                  >
                    Upload Music
                  </Button>
                )}
              </Box>
            </Grid>
          </Grid>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              onClick={handleSaveSettings}
              disabled={loading}
              size="large"
            >
              {loading ? 'Saving...' : 'Save Settings'}
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Upload Dialog */}
      <Dialog 
        open={uploadDialog.open} 
        onClose={() => setUploadDialog({ open: false, type: '' })}
        maxWidth="sm" 
        fullWidth
      >
        <DialogTitle>
          Upload {uploadDialog.type?.replace('-', ' ')}
        </DialogTitle>
        <DialogContent>
          <input
            type="file"
            accept={uploadDialog.type === 'background-music' ? 'audio/*' : 'video/*'}
            onChange={(e) => {
              const file = e.target.files[0];
              if (file) {
                handleFileUpload(file, uploadDialog.type);
              }
            }}
            style={{ width: '100%', padding: '10px' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialog({ open: false, type: '' })}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default ProjectSettings;
