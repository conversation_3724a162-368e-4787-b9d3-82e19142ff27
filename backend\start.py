#!/usr/bin/env python3
"""
Simple startup script for UniDynamics backend
This script handles common startup issues and provides better error messages
"""

import os
import sys

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'flask',
        'sqlalchemy',
        'celery',
        'redis',
        'psycopg2',
        'whisper',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install them with: pip install -r requirements.txt")
        return False
    
    return True

def check_environment():
    """Check if environment is properly configured."""
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("💡 Copy .env.example to .env and configure it")
        return False
    
    return True

def main():
    """Main startup function."""
    print("🚀 Starting UniDynamics Backend")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    print("✅ All checks passed, starting Flask app...")
    print()
    
    # Import and run the app
    try:
        from app import create_app
        app = create_app()
        
        print("🌐 Backend API starting on http://localhost:5000")
        print("📚 API documentation: http://localhost:5000/health")
        print("🛑 Press Ctrl+C to stop")
        print()
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you've installed all dependencies: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
